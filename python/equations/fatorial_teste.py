import math
import random
from itertools import combinations
import os

def clear():
    os.system('cls' if os.name == 'nt' else 'clear')

clear()

def safe_fact(n):
    try:
        if n < 0 or not n.is_integer():
            return None
        return math.factorial(int(n))
    except:
        return None

def find_factorial_equation(inputs, outputs):
    # Memória de equações já testadas
    tried_equations = set()
    
    # Primeiro verifica padrões óbvios
    def test_simple_patterns():
        patterns = [
            ("n!", lambda n: safe_fact(n)),
            ("(n-1)!", lambda n: safe_fact(n-1)),
            ("(2n)!", lambda n: safe_fact(2*n)),
            ("(3n)!", lambda n: safe_fact(3*n)),
            ("n!/2", lambda n: safe_fact(n)//2 if safe_fact(n) else None),
            ("(n+1)!/n", lambda n: safe_fact(n+1)//n if safe_fact(n+1) and n!=0 else None)
        ]
        
        for name, pattern in patterns:
            signature = f"PATTERN:{name}"  # Assinatura única para padrões
            if signature in tried_equations:
                continue
                
            tried_equations.add(signature)
            try:
                if all(pattern(n) == y for n, y in zip(inputs, outputs)):
                    return name, pattern, len(tried_equations)
            except:
                continue
        return None, None, len(tried_equations)

    # Termos para combinar
    terms = [
        ('n', lambda n: n),
        ('n+1', lambda n: n + 1),
        ('n-1', lambda n: n - 1),
        ('2n', lambda n: 2 * n),
        ('3n', lambda n: 3 * n),
        ('4n', lambda n: 4 * n),
        ('n/2', lambda n: n/2 if n%2 == 0 else None)
    ]

    operations = [
        ('*', lambda a, b: a * b),
        ('/', lambda a, b: a / b if b != 0 else None),
        ('+', lambda a, b: a + b),
        ('-', lambda a, b: a - b)
    ]

    # Primeiro testa padrões simples
    pattern_name, pattern_func, tries = test_simple_patterns()
    if pattern_name:
        return pattern_func, pattern_name, tries

    print("🔍 Padrão não óbvio. Buscando combinações...")
    
    attempt = tries
    while True:
        attempt += 1
        
        # Gera uma equação única
        while True:
            num_terms = random.choice([2, 3])
            selected_terms = random.sample(terms, num_terms)
            selected_ops = random.choices(operations, k=num_terms-1)
            
            # Constrói a assinatura da equação
            if num_terms == 2:
                eq_str = f"{selected_terms[0][0]}!{selected_ops[0][0]}{selected_terms[1][0]}!"
            else:
                eq_str = f"{selected_terms[0][0]}!{selected_ops[0][0]}({selected_terms[1][0]}!{selected_ops[1][0]}{selected_terms[2][0]}!)"
            
            if eq_str not in tried_equations:
                tried_equations.add(eq_str)
                break

        # Constrói e testa a equação
        try:
            if num_terms == 2:
                eq_func = lambda n: selected_ops[0][1](
                    safe_fact(selected_terms[0][1](n)),
                    safe_fact(selected_terms[1][1](n))
                )
            else:
                eq_func = lambda n: selected_ops[0][1](
                    safe_fact(selected_terms[0][1](n)),
                    selected_ops[1][1](
                        safe_fact(selected_terms[1][1](n)),
                        safe_fact(selected_terms[2][1](n))
                    )
                )

            valid = True
            for n, y in zip(inputs, outputs):
                res = eq_func(n)
                if res is None or not math.isclose(res, y, rel_tol=1e-5):
                    valid = False
                    break

            if valid:
                return eq_func, eq_str.replace("!", "! ").replace("*", " * ").replace("/", " / ").replace("+", " + ").replace("-", " - "), attempt

            if attempt % 1000 == 0:
                print(f"Tentativa {attempt}: Testando {eq_str}")

        except:
            continue

# Teste
inputs = [1, 2, 3, 4]
outputs = [1, 1, 1, 2]

equation, eq_str, tries = find_factorial_equation(inputs, outputs)
print(f"\n🎯 Equação encontrada na tentativa {tries}:")
print(f"f(n) = {eq_str}")
print("\nValidação:")
for n, y in zip(inputs, outputs):
    res = equation(n)
    print(f"f({n}) = {res} (esperado: {y})")