import json
import os
import math
import random
from typing import List, Tuple, Dict, Any, Callable
from itertools import combinations, permutations

class EquationFinder:
    def __init__(self, memory_file="equation_memory.json"):
        """
        Inicializa o buscador de equações com sistema de memória
        """
        self.memory_file = memory_file
        self.memory = self.load_memory()
        self.attempts = 0
        self.found_equation = None
        self.equation_string = ""
        
        # Operações matemáticas disponíveis
        self.operations = {
            '+': lambda x, y: x + y,
            '-': lambda x, y: x - y,
            '*': lambda x, y: x * y,
            '/': lambda x, y: x / y if y != 0 else float('inf'),
            '**': lambda x, y: x ** y if abs(y) < 10 else float('inf'),
            'sqrt': lambda x: math.sqrt(abs(x)),
            'log': lambda x: math.log(abs(x)) if x > 0 else float('inf'),
            'sin': lambda x: math.sin(x),
            'cos': lambda x: math.cos(x),
            'tan': lambda x: math.tan(x),
            'abs': lambda x: abs(x)
        }
        
    def load_memory(self) -> Dict:
        """Carrega a memória de equações já testadas"""
        if os.path.exists(self.memory_file):
            try:
                with open(self.memory_file, 'r') as f:
                    return json.load(f)
            except:
                return {}
        return {}
    
    def save_memory(self):
        """Salva a memória no arquivo"""
        with open(self.memory_file, 'w') as f:
            json.dump(self.memory, f, indent=2)
    
    def create_data_key(self, inputs: List[float], outputs: List[float]) -> str:
        """Cria uma chave única para os dados de entrada e saída"""
        return f"{inputs}_{outputs}"
    
    def generate_simple_equations(self, num_vars: int) -> List[str]:
        """Gera equações simples baseadas no número de variáveis"""
        equations = []
        
        if num_vars == 1:
            # Equações com uma variável
            equations.extend([
                "x",
                "x + 1", "x - 1", "x + 2", "x - 2", "x + 3", "x - 3",
                "2*x", "3*x", "4*x", "5*x", "x/2", "x/3",
                "x**2", "x**3", "sqrt(x)",
                "x**2 + x", "x**2 - x", "x**2 + 1",
                "2*x + 1", "2*x + 2", "2*x + 3", "2*x - 1", "2*x - 2",
                "3*x + 1", "3*x + 2", "3*x - 1", "3*x - 2",
                "x/2 + 1", "x/2 + 2", "x/3 + 1",
                "sin(x)", "cos(x)", "abs(x)",
                "x**2 + 2*x + 1", "x**3 - x"
            ])
        
        elif num_vars == 2:
            # Equações com duas variáveis
            equations.extend([
                "x + y", "x - y", "x * y", "x / y",
                "x**2 + y**2", "x**2 - y**2",
                "2*x + y", "x + 2*y", "x - 2*y",
                "(x + y)/2", "sqrt(x**2 + y**2)",
                "x*y + x + y", "x**2 + y", "x + y**2"
            ])
        
        return equations
    
    def evaluate_equation(self, equation_str: str, variables: Dict[str, float]) -> float:
        """Avalia uma equação com as variáveis fornecidas"""
        try:
            # Substitui funções matemáticas
            equation = equation_str.replace('sqrt', 'math.sqrt')
            equation = equation.replace('sin', 'math.sin')
            equation = equation.replace('cos', 'math.cos')
            equation = equation.replace('tan', 'math.tan')
            equation = equation.replace('log', 'math.log')
            equation = equation.replace('abs', 'abs')
            
            # Substitui variáveis
            for var, value in variables.items():
                equation = equation.replace(var, str(value))
            
            result = eval(equation)
            return float(result) if not math.isnan(result) and math.isfinite(result) else float('inf')
        except:
            return float('inf')
    
    def test_equation(self, equation_str: str, inputs: List[List[float]], outputs: List[float], tolerance: float = 1e-6) -> bool:
        """Testa se uma equação funciona para todos os dados"""
        self.attempts += 1
        
        for i, input_vals in enumerate(inputs):
            if len(input_vals) == 1:
                variables = {'x': input_vals[0]}
            elif len(input_vals) == 2:
                variables = {'x': input_vals[0], 'y': input_vals[1]}
            else:
                # Para mais variáveis, usa x1, x2, x3, etc.
                variables = {f'x{j+1}' if j > 0 else 'x': val for j, val in enumerate(input_vals)}
            
            result = self.evaluate_equation(equation_str, variables)
            
            if abs(result - outputs[i]) > tolerance:
                return False
        
        return True
    
    def find_equation(self, inputs: List[List[float]], outputs: List[float], max_attempts: int = 1000) -> Tuple[str, bool]:
        """
        Encontra uma equação que mapeia as entradas para as saídas
        """
        # Verifica se já temos essa combinação na memória
        data_key = self.create_data_key(inputs, outputs)
        if data_key in self.memory:
            print(f"Equação encontrada na memória: {self.memory[data_key]}")
            return self.memory[data_key], True
        
        self.attempts = 0
        num_vars = len(inputs[0]) if inputs else 1
        
        print(f"Procurando equação para {len(inputs)} pontos com {num_vars} variável(is)...")
        print(f"Dados de entrada: {inputs}")
        print(f"Dados de saída: {outputs}")
        print("-" * 50)
        
        # Gera equações candidatas
        candidate_equations = self.generate_simple_equations(num_vars)
        
        # Testa cada equação
        for equation in candidate_equations:
            if self.attempts >= max_attempts:
                break
                
            if self.test_equation(equation, inputs, outputs):
                self.found_equation = equation
                self.equation_string = equation
                
                # Salva na memória
                self.memory[data_key] = equation
                self.save_memory()
                
                print(f"✅ Equação encontrada após {self.attempts} tentativas!")
                print(f"Equação: f(x) = {equation}")
                return equation, True
        
        print(f"❌ Nenhuma equação encontrada após {self.attempts} tentativas.")
        return None, False
    
    def verify_equation(self, equation: str, inputs: List[List[float]], outputs: List[float]) -> bool:
        """Verifica se a equação encontrada realmente funciona"""
        print("\n🔍 Verificando a equação encontrada:")
        print(f"Equação: f(x) = {equation}")
        print("-" * 30)
        
        all_correct = True
        for i, (input_vals, expected_output) in enumerate(zip(inputs, outputs)):
            if len(input_vals) == 1:
                variables = {'x': input_vals[0]}
                input_str = f"x = {input_vals[0]}"
            elif len(input_vals) == 2:
                variables = {'x': input_vals[0], 'y': input_vals[1]}
                input_str = f"x = {input_vals[0]}, y = {input_vals[1]}"
            else:
                variables = {f'x{j+1}' if j > 0 else 'x': val for j, val in enumerate(input_vals)}
                input_str = ", ".join([f"x{j+1 if j > 0 else ''} = {val}" for j, val in enumerate(input_vals)])
            
            calculated = self.evaluate_equation(equation, variables)
            is_correct = abs(calculated - expected_output) < 1e-6
            
            status = "✅" if is_correct else "❌"
            print(f"{status} {input_str} → Esperado: {expected_output}, Calculado: {calculated:.6f}")
            
            if not is_correct:
                all_correct = False
        
        print(f"\n{'✅ Verificação bem-sucedida!' if all_correct else '❌ Verificação falhou!'}")
        return all_correct

def main():
    """Função principal para demonstrar o uso"""
    finder = EquationFinder()
    
    print("=== BUSCADOR DE EQUAÇÕES ===")
    print("Digite os dados de entrada e saída para encontrar uma equação.")
    print("Exemplo: para f(x) = 2x + 1, use entradas [1,2,3] e saídas [3,5,7]")
    print()
    
    try:
        # Entrada dos dados
        print("Digite as entradas (uma por linha, termine com linha vazia):")
        inputs = []
        while True:
            line = input().strip()
            if not line:
                break
            # Permite entrada como lista ou valor único
            if ',' in line:
                inputs.append([float(x.strip()) for x in line.split(',')])
            else:
                inputs.append([float(line)])
        
        print("Digite as saídas correspondentes (uma por linha):")
        outputs = []
        for i in range(len(inputs)):
            output = float(input(f"Saída para entrada {inputs[i]}: "))
            outputs.append(output)
        
        # Busca a equação
        equation, found = finder.find_equation(inputs, outputs)
        
        if found:
            # Verifica a equação
            finder.verify_equation(equation, inputs, outputs)
            
            print(f"\n📊 Estatísticas:")
            print(f"Tentativas realizadas: {finder.attempts}")
            print(f"Equação final: f(x) = {equation}")
        else:
            print("Não foi possível encontrar uma equação simples para esses dados.")
            print("Tente com dados que sigam um padrão matemático mais simples.")
    
    except KeyboardInterrupt:
        print("\nPrograma interrompido pelo usuário.")
    except Exception as e:
        print(f"Erro: {e}")

if __name__ == "__main__":
    main()
