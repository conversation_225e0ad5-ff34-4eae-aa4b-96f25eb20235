import math
import numpy as np
from gplearn.genetic import SymbolicRegressor
from gplearn.functions import make_function

# Versão completamente segura das funções fatoriais
def safe_fact(x):
    result = np.zeros_like(x, dtype=float)
    for i, val in enumerate(x):
        try:
            if val < 0 or not float(val).is_integer():
                result[i] = np.nan
            else:
                result[i] = math.factorial(int(val))
        except:
            result[i] = np.nan
    return result

def fact_plus1(x):
    return safe_fact(x + 1)

def fact_minus1(x):
    return safe_fact(x - 1)

def fact_2n(x):
    return safe_fact(2 * x)

def fact_3n(x):
    return safe_fact(3 * x)

def fact_n_div_2(x):
    result = np.zeros_like(x, dtype=float)
    for i, val in enumerate(x):
        if val % 2 == 0:
            result[i] = safe_fact(np.array([val / 2]))[0]
        else:
            result[i] = np.nan
    return result

# Função para criar operações seguras para o gplearn
def make_fact_function(func, name):
    def wrapped(x):
        # Garante que o input seja um array numpy
        x = np.asarray(x)
        # Converte para float para evitar problemas com tipos
        x_float = x.astype(float)
        # Aplica a função original
        result = func(x_float)
        # Verifica por infinitos ou NaNs
        result[~np.isfinite(result)] = np.nan
        return result
    
    # Testa a função com casos negativos para passar na verificação do gplearn
    test_input = np.array([-2, -1, 0, 1, 2, 3])
    try:
        wrapped(test_input)
    except:
        # Se falhar, retorna NaN para todos os valores
        def safe_wrapped(x):
            x = np.asarray(x)
            return np.full(x.shape, np.nan)
        return make_function(function=safe_wrapped, name=name, arity=1)
    
    return make_function(function=wrapped, name=name, arity=1)

# Criando as funções fatoriais para o gplearn
factorial_func = make_fact_function(safe_fact, 'fact')
fact_plus1_func = make_fact_function(fact_plus1, 'fact+1')
fact_minus1_func = make_fact_function(fact_minus1, 'fact-1')
fact_2n_func = make_fact_function(fact_2n, 'fact2n')
fact_3n_func = make_fact_function(fact_3n, 'fact3n')
fact_n_div_2_func = make_fact_function(fact_n_div_2, 'factn/2')

def find_factorial_equation_gplearn(inputs, outputs):
    # Configuração do SymbolicRegressor
    function_set = ['add', 'sub', 'mul', 'div',
                   factorial_func, fact_plus1_func, fact_minus1_func,
                   fact_2n_func, fact_3n_func, fact_n_div_2_func]
    
    est_gp = SymbolicRegressor(
        population_size=1000,  # Reduzido para desempenho
        generations=10,
        stopping_criteria=0.01,
        p_crossover=0.7,
        p_subtree_mutation=0.1,
        p_hoist_mutation=0.05,
        p_point_mutation=0.1,
        max_samples=0.9,
        verbose=1,
        parsimony_coefficient=0.01,
        function_set=function_set,
        random_state=42,
        metric='mean absolute error',
        n_jobs=-1
    )
    
    # Preparando os dados
    X = np.array(inputs).reshape(-1, 1)
    y = np.array(outputs)
    
    # Treinando o modelo
    est_gp.fit(X, y)
    
    # Obtendo a melhor equação
    best_equation = est_gp._program
    equation_str = str(best_equation)
    
    # Função para avaliar a equação encontrada
    def equation_func(n):
        return best_equation.execute(np.array([n]).reshape(-1, 1))[0]
    
    return equation_func, equation_str, est_gp.run_details_['generation']

# Teste
inputs = [1, 2, 3, 4]
outputs = [1, 1, 1, 2]

print("🔍 Procurando equação usando programação genética...")
try:
    equation, eq_str, generations = find_factorial_equation_gplearn(inputs, outputs)
    
    print(f"\n🎯 Equação encontrada na geração {generations}:")
    print(f"f(n) = {eq_str}")
    print("\nValidação:")
    for n, y in zip(inputs, outputs):
        res = equation(n)
        print(f"f({n}) = {res:.2f} (esperado: {y})")
except Exception as e:
    print(f"Erro durante a execução: {str(e)}")
    print("Verifique se todas as dependências estão instaladas corretamente.")