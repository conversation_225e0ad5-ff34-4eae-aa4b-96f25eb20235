import math
import numpy as np
from pysr import PySRRegressor

X = np.array([1, 2, 3, 4]).reshape(-1, 1)
y = np.array([1, 1, 1, 2])

def factorial(x):
    return np.array([math.factorial(int(i)) if i >= 0 and i.is_integer() else np.nan for i in x])

model = PySRRegressor(
    niterations=20,
    binary_operators=["+", "-", "*", "/"],
    unary_operators=["factorial(x)", "sqrt(x)"],
    extra_sympy_mappings={'factorial': lambda x: math.factorial(int(x))},
    loss="L1DistLoss()",
    verbose=True
)

model.fit(X, y)

print(model)