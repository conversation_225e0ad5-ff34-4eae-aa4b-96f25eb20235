# 🧮 Buscador de Equações

Um programa Python que encontra automaticamente equações matemáticas a partir de dados de entrada e saída fornecidos pelo usuário.

## 🌟 Funcionalidades

- ✅ **Busca automática de equações** - Encontra equações que mapeiam entradas para saídas
- 🧠 **Sistema de memória** - Evita repetir cálculos para os mesmos dados
- 📊 **Contador de tentativas** - Mostra quantas tentativas foram necessárias
- 🔍 **Verificação automática** - Usa a equação encontrada para verificar os dados
- 📈 **Suporte a múltiplas variáveis** - Funciona com 1 ou 2 variáveis de entrada
- 💾 **Persistência** - Salva equações encontradas em arquivo JSON

## 🚀 Como Usar

### Execução Rápida

```bash
python exemplo_uso.py
```

### Execução com Testes

```bash
python test_equation_finder.py
```

### Uso Programático

```python
from equation_finder import EquationFinder

# Criar o buscador
finder = EquationFinder()

# Dados de exemplo: f(x) = 2x + 1
inputs = [[1], [2], [3], [4]]    # x = 1, 2, 3, 4
outputs = [3, 5, 7, 9]           # f(x) = 3, 5, 7, 9

# Buscar equação
equation, found = finder.find_equation(inputs, outputs)

if found:
    print(f"Equação encontrada: {equation}")
    # Verificar se funciona
    finder.verify_equation(equation, inputs, outputs)
```

## 📝 Exemplos

### Exemplo 1: Equação Linear
```
Entradas: [1], [2], [3], [4]
Saídas: [3, 5, 7, 9]
Resultado: f(x) = 2*x + 1
```

### Exemplo 2: Equação Quadrática
```
Entradas: [1], [2], [3], [4]
Saídas: [1, 4, 9, 16]
Resultado: f(x) = x**2
```

### Exemplo 3: Duas Variáveis
```
Entradas: [2,3], [4,5], [1,6]
Saídas: [6, 20, 6]
Resultado: f(x,y) = x * y
```

## 🔧 Tipos de Equações Suportadas

### Uma Variável (x)
- Lineares: `x`, `2*x + 1`, `3*x - 2`
- Quadráticas: `x**2`, `x**2 + x`, `x**2 + 1`
- Cúbicas: `x**3`, `x**3 - x`
- Raízes: `sqrt(x)`
- Trigonométricas: `sin(x)`, `cos(x)`
- Outras: `abs(x)`, `x/2 + 1`

### Duas Variáveis (x, y)
- Básicas: `x + y`, `x - y`, `x * y`, `x / y`
- Quadráticas: `x**2 + y**2`, `x**2 - y**2`
- Combinadas: `2*x + y`, `x + 2*y`, `x*y + x + y`
- Geométricas: `sqrt(x**2 + y**2)`, `(x + y)/2`

## 📁 Arquivos

- `equation_finder.py` - Classe principal do buscador
- `exemplo_uso.py` - Exemplos simples de uso
- `test_equation_finder.py` - Testes automáticos e interativos
- `equation_memory.json` - Arquivo de memória (criado automaticamente)

## 🎯 Como Funciona

1. **Entrada de Dados**: Você fornece pares de entrada-saída
2. **Geração de Candidatos**: O programa gera equações matemáticas candidatas
3. **Teste**: Cada equação é testada com todos os dados fornecidos
4. **Verificação**: A equação encontrada é verificada novamente
5. **Memória**: A equação é salva para evitar recálculos futuros

## 💡 Dicas de Uso

- **Dados Consistentes**: Certifique-se de que seus dados seguem um padrão matemático
- **Poucos Pontos**: Comece com 3-5 pontos de dados
- **Padrões Simples**: O programa funciona melhor com equações matemáticas básicas
- **Precisão**: Use dados com boa precisão numérica

## 🔍 Exemplo de Saída

```
=== BUSCADOR DE EQUAÇÕES ===
Procurando equação para 4 pontos com 1 variável(is)...
Dados de entrada: [[1], [2], [3], [4]]
Dados de saída: [3, 5, 7, 9]
--------------------------------------------------
✅ Equação encontrada após 15 tentativas!
Equação: f(x) = 2*x + 1

🔍 Verificando a equação encontrada:
Equação: f(x) = 2*x + 1
------------------------------
✅ x = 1 → Esperado: 3, Calculado: 3.000000
✅ x = 2 → Esperado: 5, Calculado: 5.000000
✅ x = 3 → Esperado: 7, Calculado: 7.000000
✅ x = 4 → Esperado: 9, Calculado: 9.000000

✅ Verificação bem-sucedida!

📊 Estatísticas:
Tentativas realizadas: 15
Equação final: f(x) = 2*x + 1
```

## 🚨 Limitações

- Funciona apenas com equações matemáticas relativamente simples
- Suporta no máximo 2 variáveis de entrada
- Pode não encontrar equações muito complexas ou não-lineares
- Requer que os dados sigam um padrão matemático exato

## 🛠️ Requisitos

- Python 3.6+
- Bibliotecas padrão: `json`, `os`, `math`, `random`, `itertools`

Nenhuma instalação adicional necessária! 🎉
