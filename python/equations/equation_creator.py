import math
import random
import os
import numpy as np
from itertools import combinations_with_replacement

def clear():
    os.system('cls' if os.name == 'nt' else 'clear')

clear()

def safe_pow(x, y):
    """Versão segura da operação de potência que evita números complexos"""
    try:
        result = x ** y
        if isinstance(result, complex):
            return float('nan')
        return result
    except (ValueError, ZeroDivisionError, OverflowError):
        return float('nan')

def safe_fact(x):
    """Versão segura do fatorial"""
    try:
        if x < 0 or x != int(x):
            return float('nan')
        return math.factorial(int(x))
    except (ValueError, OverflowError):
        return float('nan')

def generate_equation(inputs, outputs, max_depth=3, max_tries=100000):
    operations = [
        ('+', lambda x, y: x + y),
        ('-', lambda x, y: x - y),
        ('*', lambda x, y: x * y),
        ('/', lambda x, y: x / y if y != 0 else float('nan')),
        ('**', safe_pow),
        ('sqrt', lambda x: math.sqrt(x) if x >= 0 else float('nan')),
        ('log', lambda x: math.log(x) if x > 0 else float('nan')),
        ('fact', safe_fact),  # Adicionando operação fatorial
    ]

    def create_random_equation(depth):
        if depth == 0:
            if random.random() < 0.7:
                return lambda x: x
            else:
                const = random.choice([1, 2, 3, 4, 5, 0.5, -1, -2])
                return lambda x: const
        
        op = random.choice(operations)
        
        if op[0] in ['sqrt', 'log', 'fact']:  # Operações unárias
            inner = create_random_equation(depth-1)
            return lambda x: op[1](inner(x))
        else:  # Operações binárias
            left = create_random_equation(depth-1)
            right = create_random_equation(depth-1)
            return lambda x: op[1](left(x), right(x))
    
    def test_equation(eq):
        try:
            for x, y in zip(inputs, outputs):
                result = eq(x)
                if math.isnan(result) or isinstance(result, complex):
                    return False
                if not math.isclose(result, y, rel_tol=1e-5, abs_tol=1e-5):
                    return False
            return True
        except (ValueError, ZeroDivisionError, OverflowError, TypeError):
            return False
    
    # Primeiro, tentamos encontrar uma equação simples
    for _ in range(max_tries):
        eq = create_random_equation(max_depth)
        if test_equation(eq):
            return eq
    
    # Se não encontrou, tentamos padrões específicos com fatoriais
    print("Tentando padrões complexos com fatoriais...")
    
    # Padrão para (4n - 2)! / ((n + 1)! * (3n - 1)!)
    try:
        eq = lambda n: math.factorial(4*n - 2) // (math.factorial(n + 1) * math.factorial(3*n - 1))
        if test_equation(eq):
            return eq
    except:
        pass
    
    # Outros padrões com fatoriais
    patterns = [
        lambda n: math.factorial(2*n) // (math.factorial(n + 1) * math.factorial(n)),
        lambda n: math.factorial(3*n) // (math.factorial(n + 2) * math.factorial(2*n - 1)),
        lambda n: math.factorial(n) * math.factorial(n + 1) // math.factorial(2*n + 1),
    ]
    
    for pattern in patterns:
        try:
            if test_equation(pattern):
                return pattern
        except:
            continue
    
    return None

def equation_to_string(eq):
    # Testa padrão de fatorial complexo
    try:
        # Verifica se corresponde a (4n - 2)! / ((n + 1)! * (3n - 1)!)
        test_values = [1, 2, 3, 4]
        expected = [math.factorial(4*n - 2) // (math.factorial(n + 1) * math.factorial(3*n - 1)) for n in test_values]
        actual = [eq(n) for n in test_values]
        
        if all(e == a for e, a in zip(expected, actual)):
            return "(4n - 2)! / ((n + 1)! * (3n - 1)!)"
    except:
        pass
    
    # Testa padrão linear (ax + b)
    try:
        x = np.array([1, 2, 3, 4])
        y = np.array([eq(xi) for xi in x])
        A = np.vstack([x, np.ones(len(x))]).T
        a, b = np.linalg.lstsq(A, y, rcond=None)[0]
        
        if all(math.isclose(eq(xi), a*xi + b, rel_tol=1e-4) for xi in x):
            if math.isclose(a, 1, rel_tol=1e-4) and math.isclose(b, 0, rel_tol=1e-4):
                return "x"
            elif math.isclose(a, 1, rel_tol=1e-4):
                return f"x + {b:.2f}".rstrip('0').rstrip('.') if b > 0 else f"x - {abs(b):.2f}".rstrip('0').rstrip('.')
            elif math.isclose(b, 0, rel_tol=1e-4):
                return f"{a:.2f}x".rstrip('0').rstrip('.')
            else:
                return f"{a:.2f}x + {b:.2f}".rstrip('0').rstrip('.') if b > 0 else f"{a:.2f}x - {abs(b):.2f}".rstrip('0').rstrip('.')
    except:
        pass
    
    # Testa padrão quadrático (ax² + bx + c)
    try:
        x = np.array([1, 2, 3, 4])
        y = np.array([eq(xi) for xi in x])
        A = np.vstack([x**2, x, np.ones(len(x))]).T
        a, b, c = np.linalg.lstsq(A, y, rcond=None)[0]
        
        if all(math.isclose(eq(xi), a*xi**2 + b*xi + c, rel_tol=1e-4) for xi in x):
            parts = []
            if not math.isclose(a, 0, rel_tol=1e-4):
                parts.append(f"{a:.2f}x²".replace(".00x²", "x²"))
            if not math.isclose(b, 0, rel_tol=1e-4):
                parts.append(f"{b:.2f}x".replace(".00x", "x"))
            if not math.isclose(c, 0, rel_tol=1e-4) or len(parts) == 0:
                parts.append(f"{c:.2f}".replace(".00", ""))
            
            equation = " + ".join(parts).replace("+ -", "- ")
            return equation if equation else "0"
    except:
        pass
    
    # Testa outras operações simples
    try:
        test_values = [1, 2, 3, 4]
        if all(math.isclose(eq(x), x**2) for x in test_values):
            return "x²"
        if all(math.isclose(eq(x), x**3) for x in test_values):
            return "x³"
        if all(math.isclose(eq(x), math.sqrt(x)) for x in test_values if x >= 0):
            return "√x"
        if all(math.isclose(eq(x), math.log(x)) for x in test_values if x > 0):
            return "log(x)"
        if all(math.isclose(eq(x), 2**x) for x in test_values):
            return "2^x"
        if all(math.isclose(eq(x), math.factorial(x)) for x in test_values):
            return "x!"
    except:
        pass
    
    # Testa constante
    try:
        c = eq(1)
        if all(math.isclose(eq(x), c) for x in test_values):
            return f"{c:.2f}".replace(".00", "")
    except:
        pass
    
    return "f(x)"

if __name__ == "__main__":
    # Exemplo com a sequência que você mencionou
    inputs = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    outputs = [1, 1, 1, 2, 3, 5, 9, 18, 35, 75]
    
    print("Procurando equação...")
    equation = generate_equation(inputs, outputs, max_depth=4, max_tries=100000)
    
    if equation:
        print("\nEquação encontrada!")
        print("Representação:", equation_to_string(equation))
        
        print("\nTestando a equação:")
        for x, y in zip(inputs, outputs):
            pred = equation(x)
            print(f"Entrada: {x}, Saída esperada: {y}, Saída da equação: {pred}")
    else:
        print("Não foi possível encontrar uma equação que se ajuste aos dados.")