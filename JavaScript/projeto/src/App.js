import React, { useState } from 'react';
import './App.css';
import './styles/index.css';
import './styles/calculator-back-buttons.css';
import CalculatorSelector from './calculators/calculatorSelector/CalculatorSelectorRouter';
import Chemistry from './calculators/chemistry/chemistry';
import Physics from './calculators/physics/physics';
import Math from './calculators/math/math';
import { BrowserRouter as Router, Route, Routes, useNavigate, useLocation } from 'react-router-dom';

// Componente para o botão de voltar
const BackButton = ({ calculatorType }) => {
  const navigate = useNavigate();
  
  const handleBack = () => {
    navigate('/');
  };

  return (
    <button
      className={`back-button ${
        calculatorType === 'chemistry' 
          ? 'back-button-right back-button-chemistry' 
          : 'back-button-left back-button-' + calculatorType
      }`}
      onClick={handleBack}
    >
      Back to Calculators
    </button>
  );
};

// Hook para atualizar as cores globais conforme a rota
function useCalculatorColors() {
  const location = useLocation();
  React.useEffect(() => {
    let primaryColor = '#000000';
    let borderColor = 'bga(0, 0, 0, 0.5)';
    if (location.pathname === '/chemistry') {
      primaryColor = '#4CAF50';
      borderColor = 'rgba(76, 175, 80, 0.5)';  
    } else if (location.pathname === '/physics') {
      primaryColor = '#9C27B0';
      borderColor = 'rgba(156, 39, 176, 0.5)';
    } else if (location.pathname === '/math') {
      primaryColor = '#2196F3';
      borderColor = 'rgba(33, 150, 243, 0.5)';
    }
    document.documentElement.style.setProperty('--primary-color', primaryColor);
    document.documentElement.style.setProperty('--border-color', borderColor);
  }, [location.pathname]);
}

// Componente de layout base
const AppLayout = ({ children }) => {
  return <div className="App">{children}</div>;
};

// Componente para a página de seleção
const SelectorPage = ({ onSelectCalculator }) => {
  return <CalculatorSelector onSelectCalculator={onSelectCalculator} />;
};

// Componente para a calculadora de química
const ChemistryPage = () => {
  return (
    <div>
      <BackButton calculatorType="chemistry" />
      <Chemistry />
    </div>
  );
};

// Componente para a calculadora de física
const PhysicsPage = () => {
  return (
    <div>
      <BackButton calculatorType="physics" />
      <Physics />
    </div>
  );
};

// Componente para a calculadora de matemática
const MathPage = () => {
  return (
    <div>
      <BackButton calculatorType="math" />
      <Math />
    </div>
  );
};

// Componente principal com cores
function AppWithColors() {
  useCalculatorColors();
  const [selectedCalculator, setSelectedCalculator] = React.useState(null);
  const handleSelectCalculator = (calculatorId) => {
    setSelectedCalculator(calculatorId);
  };
  return (
    <Routes>
      <Route 
        path="/" 
        element={
          <AppLayout>
            <SelectorPage onSelectCalculator={handleSelectCalculator} />
          </AppLayout>
        } 
      />
      <Route 
        path="/chemistry" 
        element={
          <AppLayout>
            <ChemistryPage />
          </AppLayout>
        } 
      />
      <Route 
        path="/physics" 
        element={
          <AppLayout>
            <PhysicsPage />
          </AppLayout>
        } 
      />
      <Route 
        path="/math" 
        element={
          <AppLayout>
            <MathPage />
          </AppLayout>
        } 
      />
    </Routes>
  );
}

// Componente principal
function App() {
  return (
    <Router>
      <AppWithColors />
    </Router>
  );
}

export default App;