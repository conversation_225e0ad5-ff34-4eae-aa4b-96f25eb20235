import React, { useEffect } from 'react';
import { Inspector } from 'react-dev-inspector';
import '../../calculators/chemistry/styles/debug/debug-visual-elements.css';

/**
 * Componente para adicionar elementos visuais de debug usando react-dev-inspector
 * Esta biblioteca é muito mais eficiente e evita travamentos
 */
const DebugVisualElements = ({ isDebugMode, config }) => {
  // Adiciona a classe debug-mode-active ao body quando o modo debug está ativo
  useEffect(() => {
    if (isDebugMode) {
      document.body.classList.add('debug-mode-active');

      // Adicionar estilos CSS para destacar elementos com base nas configurações
      const styleElement = document.createElement('style');
      styleElement.id = 'debug-styles';

      let cssRules = '';

      // Adicionar regras CSS com base nas configurações
      if (config.showOutlines) {
        cssRules += `
          .calculator-block { outline: 2px dashed red !important; position: relative; }
          .conversion-container { outline: 2px dashed blue !important; position: relative; }
          .conditions-container { outline: 2px dashed green !important; position: relative; }
          .presets-container { outline: 2px dashed orange !important; position: relative; }
          .conversion-box { outline: 2px dashed purple !important; position: relative; }
          .condition-box { outline: 2px dashed cyan !important; position: relative; }
          .conversion-input { outline: 2px dashed magenta !important; position: relative; }
          .condition-input { outline: 2px dashed yellow !important; position: relative; }
          .preset-button { outline: 2px dashed orange !important; position: relative; }
          .compound-container { outline: 2px dashed gold !important; position: relative; }
          .gas-law-container { outline: 2px dashed teal !important; position: relative; }
        `;
      }

      if (config.showDimensions) {
        cssRules += `
          .calculator-block .dimension-label,
          .conversion-container .dimension-label,
          .conditions-container .dimension-label,
          .presets-container .dimension-label,
          .compound-container .dimension-label,
          .gas-law-container .dimension-label,
          .conversion-box .dimension-label,
          .condition-box .dimension-label,
          .conversion-input .dimension-label,
          .condition-input .dimension-label,
          .preset-button .dimension-label {
            position: absolute;
            top: 2px;
            right: 2px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            font-size: 10px;
            padding: 2px 4px;
            border-radius: 2px;
            z-index: 9999;
            pointer-events: none;
          }
        `;

        // Adicionar script para atualizar as dimensões
        const dimensionsScript = document.createElement('script');
        dimensionsScript.id = 'debug-dimensions-script';
        dimensionsScript.textContent = "\n\
          function updateDimensions() {\n\
            const elements = document.querySelectorAll('.calculator-block, .conversion-container, .conditions-container, .presets-container, .compound-container, .gas-law-container, .conversion-box, .condition-box, .conversion-input, .condition-input, .preset-button');\n\
            // Remover labels de dimensões existentes\n\
            document.querySelectorAll('.dimension-label').forEach(function(el) {\n\
              el.parentNode.removeChild(el);\n\
            });\n\
            \n\
            elements.forEach(function(el) {\n\
              const rect = el.getBoundingClientRect();\n\
              if (rect.width > 0 && rect.height > 0) {\n\
                // Verificar se o elemento já tem position relative ou absolute\n\
                const position = window.getComputedStyle(el).position;\n\
                if (position === 'static') {\n\
                  el.setAttribute('data-original-position', 'static');\n\
                  el.style.position = 'relative';\n\
                }\n\
                \n\
                // Criar label de dimensões\n\
                const dimensionLabel = document.createElement('div');\n\
                dimensionLabel.className = 'dimension-label';\n\
                dimensionLabel.textContent = Math.round(rect.width) + 'x' + Math.round(rect.height);\n\
                el.appendChild(dimensionLabel);\n\
              }\n\
            });\n\
          }\n\
          \n\
          // Atualizar dimensões inicialmente\n\
          updateDimensions();\n\
          \n\
          // Atualizar dimensões quando a janela for redimensionada\n\
          window.addEventListener('resize', updateDimensions);\n\
          \n\
          // Atualizar dimensões periodicamente\n\
          var dimensionsInterval = setInterval(updateDimensions, 500);\n\
          \n\
          // Armazenar o intervalo para poder limpar depois\n\
          window.debugDimensionsInterval = dimensionsInterval;\n\
        ";
        document.head.appendChild(dimensionsScript);
      }

      if (config.showConversionControls) {
        cssRules += `
          .conversion-controls { outline: 2px dashed purple !important; }
          .swap-button { outline: 2px dashed cyan !important; }
          .copy-button { outline: 2px dashed magenta !important; }
        `;
      }

      if (config.showConditionBox) {
        cssRules += `
          .condition-box { outline: 2px dashed yellow !important; }
          .condition-input { outline: 2px dashed lime !important; }
        `;
      }

      if (config.showConversions) {
        cssRules += `
          .conversion-container { background-color: rgba(0, 0, 255, 0.1) !important; }
          .conversion-input { outline: 2px dashed blue !important; }
        `;
      }

      if (config.showConditions) {
        cssRules += `
          .conditions-container { background-color: rgba(0, 255, 0, 0.1) !important; }
        `;
      }

      if (config.showPresets) {
        cssRules += `
          .presets-container { background-color: rgba(255, 165, 0, 0.1) !important; }
          .preset-button { outline: 2px dashed orange !important; }
        `;
      }

      if (config.showCompound) {
        cssRules += `
          .compound-container { background-color: rgba(255, 255, 0, 0.1) !important; }
          .compound-input { outline: 2px dashed yellow !important; }
          .compound-result { outline: 2px dashed gold !important; }
        `;
      }

      if (config.showGasLaw) {
        cssRules += `
          .gas-law-container { background-color: rgba(255, 0, 255, 0.1) !important; }
          .gas-law-input { outline: 2px dashed magenta !important; }
          .gas-law-result { outline: 2px dashed purple !important; }
        `;
      }

      if (config.showCenterLines) {
        cssRules += `
          .calculator-block .center-line-horizontal {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background-color: rgba(255, 0, 0, 0.5);
            pointer-events: none;
            z-index: 9999;
          }

          .calculator-block .center-line-vertical {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 1px;
            background-color: rgba(255, 0, 0, 0.5);
            pointer-events: none;
            z-index: 9999;
          }
        `;

        // Adicionar script para criar linhas centrais
        const centerLinesScript = document.createElement('script');
        centerLinesScript.id = 'debug-center-lines-script';
        centerLinesScript.textContent = "\n\
          function createCenterLines() {\n\
            const elements = document.querySelectorAll('.calculator-block');\n\
            // Remover linhas centrais existentes\n\
            document.querySelectorAll('.center-line-horizontal, .center-line-vertical').forEach(function(el) {\n\
              el.parentNode.removeChild(el);\n\
            });\n\
            \n\
            // Criar novas linhas centrais\n\
            elements.forEach(function(el) {\n\
              const horizontalLine = document.createElement('div');\n\
              horizontalLine.className = 'center-line-horizontal';\n\
              el.appendChild(horizontalLine);\n\
              \n\
              const verticalLine = document.createElement('div');\n\
              verticalLine.className = 'center-line-vertical';\n\
              el.appendChild(verticalLine);\n\
            });\n\
          }\n\
          \n\
          // Criar linhas centrais inicialmente\n\
          createCenterLines();\n\
          \n\
          // Atualizar linhas centrais quando a janela for redimensionada\n\
          window.addEventListener('resize', createCenterLines);\n\
          \n\
          // Armazenar o event listener para poder remover depois\n\
          window.debugCenterLinesResizeListener = createCenterLines;\n\
        ";
        document.head.appendChild(centerLinesScript);
      }

      styleElement.textContent = cssRules;
      document.head.appendChild(styleElement);

      // Adicionar o Inspector para inspeção interativa
      const inspectorContainer = document.createElement('div');
      inspectorContainer.id = 'debug-inspector-container';
      document.body.appendChild(inspectorContainer);

      // Adicionar um event listener para atualizar os estilos quando as configurações mudam
      const handleConfigChange = () => {
        const existingStyle = document.getElementById('debug-styles');
        if (existingStyle) {
          existingStyle.parentNode.removeChild(existingStyle);
        }

        // Recriar os estilos com as novas configurações
        const newStyleElement = document.createElement('style');
        newStyleElement.id = 'debug-styles';
        newStyleElement.textContent = cssRules;
        document.head.appendChild(newStyleElement);
      };

      document.addEventListener('debug-config-changed', handleConfigChange);

      return () => {
        // Remover estilos CSS quando o modo debug é desativado
        document.body.classList.remove('debug-mode-active');
        const styleEl = document.getElementById('debug-styles');
        if (styleEl) {
          styleEl.parentNode.removeChild(styleEl);
        }

        // Remover o container do Inspector
        const inspectorEl = document.getElementById('debug-inspector-container');
        if (inspectorEl) {
          inspectorEl.parentNode.removeChild(inspectorEl);
        }

        // Remover o script de dimensões
        const dimensionsScript = document.getElementById('debug-dimensions-script');
        if (dimensionsScript) {
          dimensionsScript.parentNode.removeChild(dimensionsScript);
        }

        // Limpar o intervalo de atualização de dimensões
        if (window.debugDimensionsInterval) {
          clearInterval(window.debugDimensionsInterval);
          window.debugDimensionsInterval = null;
        }

        // Remover todos os elementos de dimensões
        document.querySelectorAll('.dimension-label').forEach(el => {
          if (el && el.parentNode) {
            el.parentNode.removeChild(el);
          }
        });

        // Restaurar a posição dos elementos que foram alterados
        document.querySelectorAll('.calculator-block, .conversion-container, .conditions-container, .presets-container, .compound-container, .gas-law-container, .conversion-box, .condition-box, .conversion-input, .condition-input, .preset-button').forEach(el => {
          if (el.style.position === 'relative' && !el.getAttribute('data-original-position')) {
            el.style.position = 'static';
          }
        });

        // Remover o script de linhas centrais
        const centerLinesScript = document.getElementById('debug-center-lines-script');
        if (centerLinesScript) {
          centerLinesScript.parentNode.removeChild(centerLinesScript);
        }

        // Remover o event listener de redimensionamento
        if (window.debugCenterLinesResizeListener) {
          window.removeEventListener('resize', window.debugCenterLinesResizeListener);
          window.debugCenterLinesResizeListener = null;
        }

        // Remover todas as linhas centrais
        document.querySelectorAll('.center-line-horizontal, .center-line-vertical').forEach(el => {
          if (el && el.parentNode) {
            el.parentNode.removeChild(el);
          }
        });

        // Remover o event listener
        document.removeEventListener('debug-config-changed', handleConfigChange);
      };
    } else {
      document.body.classList.remove('debug-mode-active');
      const styleEl = document.getElementById('debug-styles');
      if (styleEl) {
        styleEl.parentNode.removeChild(styleEl);
      }

      // Remover o container do Inspector
      const inspectorEl = document.getElementById('debug-inspector-container');
      if (inspectorEl) {
        inspectorEl.parentNode.removeChild(inspectorEl);
      }

      // Remover o script de dimensões
      const dimensionsScript = document.getElementById('debug-dimensions-script');
      if (dimensionsScript) {
        dimensionsScript.parentNode.removeChild(dimensionsScript);
      }

      // Limpar o intervalo de atualização de dimensões
      if (window.debugDimensionsInterval) {
        clearInterval(window.debugDimensionsInterval);
        window.debugDimensionsInterval = null;
      }

      // Remover todos os elementos de dimensões
      document.querySelectorAll('.dimension-label').forEach(el => {
        if (el && el.parentNode) {
          el.parentNode.removeChild(el);
        }
      });

      // Restaurar a posição dos elementos que foram alterados
      document.querySelectorAll('.calculator-block, .conversion-container, .conditions-container, .presets-container, .compound-container, .gas-law-container, .conversion-box, .condition-box, .conversion-input, .condition-input, .preset-button').forEach(el => {
        if (el.style.position === 'relative' && !el.getAttribute('data-original-position')) {
          el.style.position = 'static';
        }
      });

      // Remover o script de linhas centrais
      const centerLinesScript = document.getElementById('debug-center-lines-script');
      if (centerLinesScript) {
        centerLinesScript.parentNode.removeChild(centerLinesScript);
      }

      // Remover o event listener de redimensionamento
      if (window.debugCenterLinesResizeListener) {
        window.removeEventListener('resize', window.debugCenterLinesResizeListener);
        window.debugCenterLinesResizeListener = null;
      }

      // Remover todas as linhas centrais
      document.querySelectorAll('.center-line-horizontal, .center-line-vertical').forEach(el => {
        if (el && el.parentNode) {
          el.parentNode.removeChild(el);
        }
      });
    }
  }, [isDebugMode, config]);

  // Renderizar o Inspector apenas quando o modo debug está ativo
  if (!isDebugMode) return null;

  return (
    <Inspector
      keys={['control', 'shift', 'i']}
      borderColor="rgba(255, 0, 0, 0.8)"
      backgroundColor="rgba(255, 0, 0, 0.1)"
    />
  );
};

export default DebugVisualElements;
