import React, { useState, useEffect, useRef } from "react";
import './ions-table.css';
import { CommonIonsData } from '../../../constants/chemistryConstants';

const commonCations = CommonIonsData.commonCations;
const commonAnions = CommonIonsData.commonAnions;

// display em lista
const IonsTable = ({ onClose }) => {
  return (
    <div className="ions-table-modal" style={{
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100vw',
      height: '100vh',
      background: 'rgba(0,0,0,0.7)',
      zIndex: 10000,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    }}>
        <div style={{
          background: '#222',
          borderRadius: 12,
          padding: 24,
          position: 'relative',
          boxShadow: '0 4px 32px #000a',
          maxHeight: '90vh',
          overflow: 'auto',
          width: '90vw',
        }}>
          <button
            onClick={onClose}
            style={{
              position: 'absolute',
              top: 10,
              right: 10,
              background: 'rgba(76, 175, 80, 0.3)',
              color: 'white',
              border: 'none',
              borderRadius: 6,
              padding: '6px 14px',
              fontWeight: 'bold',
              cursor: 'pointer',
              fontSize: 16,
              zIndex: 2,
            }}
          >
            X
          </button>
          <h2 style={{
            color: '#4fc3f7',
            textAlign: 'center',
            marginBottom: '30px',
            borderBottom: '2px solid rgba(76, 175, 80, 0.3)',
            paddingBottom: '10px'
          }}>Common Ions</h2>

          <div className="ions-table-container">
            {/* Tabela de Cátions */}
            <div className="ions-section">
              <h3 className="section-title cations-title">Cations (Positive Ions)</h3>
              <div className="two-column-layout">
                {/* Primeira coluna de cátions */}
                <div className="table-wrapper">
                  <table className="ions-grid-table">
                    <thead>
                      <tr>
                        <th className="table-header">Ion Symbol</th>
                        <th className="table-header">Ion Name</th>
                      </tr>
                    </thead>
                    <tbody>
                      {commonCations.slice(0, Math.ceil(commonCations.length / 2)).map((ion, index) => (
                        <tr key={ion.symbol} className={`table-row ${index % 2 === 0 ? 'even-row' : 'odd-row'}`}>
                          <td className="ion-symbol-cell">{ion.symbol}</td>
                          <td className="ion-name-cell">
                            {Array.isArray(ion.name) ? ion.name.join(' / ') : ion.name}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Segunda coluna de cátions */}
                <div className="table-wrapper">
                  <table className="ions-grid-table">
                    <thead>
                      <tr>
                        <th className="table-header">Ion Symbol</th>
                        <th className="table-header">Ion Name</th>
                      </tr>
                    </thead>
                    <tbody>
                      {commonCations.slice(Math.ceil(commonCations.length / 2)).map((ion, index) => (
                        <tr key={ion.symbol} className={`table-row ${index % 2 === 0 ? 'even-row' : 'odd-row'}`}>
                          <td className="ion-symbol-cell">{ion.symbol}</td>
                          <td className="ion-name-cell">
                            {Array.isArray(ion.name) ? ion.name.join(' / ') : ion.name}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            {/* Tabela de Ânions */}
            <div className="ions-section">
              <h3 className="section-title anions-title">Anions (Negative Ions)</h3>
              <div className="two-column-layout">
                {/* Primeira coluna de ânions */}
                <div className="table-wrapper">
                  <table className="ions-grid-table">
                    <thead>
                      <tr>
                        <th className="table-header">Ion Symbol</th>
                        <th className="table-header">Ion Name</th>
                      </tr>
                    </thead>
                    <tbody>
                      {commonAnions.slice(0, Math.ceil(commonAnions.length / 2)).map((ion, index) => (
                        <tr key={ion.symbol} className={`table-row ${index % 2 === 0 ? 'even-row' : 'odd-row'}`}>
                          <td className="ion-symbol-cell">{ion.symbol}</td>
                          <td className="ion-name-cell">
                            {Array.isArray(ion.name) ? ion.name.join(' / ') : ion.name}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Segunda coluna de ânions */}
                <div className="table-wrapper">
                  <table className="ions-grid-table">
                    <thead>
                      <tr>
                        <th className="table-header">Ion Symbol</th>
                        <th className="table-header">Ion Name</th>
                      </tr>
                    </thead>
                    <tbody>
                      {commonAnions.slice(Math.ceil(commonAnions.length / 2)).map((ion, index) => (
                        <tr key={ion.symbol} className={`table-row ${index % 2 === 0 ? 'even-row' : 'odd-row'}`}>
                          <td className="ion-symbol-cell">{ion.symbol}</td>
                          <td className="ion-name-cell">
                            {Array.isArray(ion.name) ? ion.name.join(' / ') : ion.name}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
    </div>
  );
};

export default IonsTable;