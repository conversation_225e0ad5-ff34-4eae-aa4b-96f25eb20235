/* eslint-disable no-unused-vars, react-hooks/exhaustive-deps */
import React, { useState, useEffect, useCallback, useRef } from 'react';
import '../../App.css';
import './styles/index.css';
import Squares from '../../Squares/Squares.jsx';
import Dock from '../../Dock/Dock.jsx';
import DebugMode from '../../components/debug/index.js';
import SimpleCarousel from './components/carousel/SimpleCarousel.js';
import CompoundCarousel from './components/carousel/CompoundCarousel.js';
import { Conversions, GasLaws, ChemistryToolsOp, ChemicalEquationsOp } from './operations/index.js';
import { calcularMassaMolar, gramsToMoles, molesToGrams, molesToAtoms, atomsToMoles, molesToVolume, volumeToMoles, convertMass, convertVolume, convertQuantity, formatScientificNotation } from './constants/chemistryUtils.js';
import { unitOptions } from './constants/chemistryConstants.js';
import {
  VscHome,
  VscArchive,
  VscAccount,
  VscSettingsGear
} from 'react-icons/vsc';
import Select from 'react-select';
import {
  CustomControl,
  CustomSingleValue,
  CustomValueContainer,
  CustomDropdownIndicator,
  CustomMenu,
  CustomMenuList,
  CustomOption
} from './components/Select.js';
import PeriodicTable from './components/dockPainels/periodic-table/periodic_table.js';
import IonsTable from './components/dockPainels/ions-table/ions-table.js';



// Componentes personalizados para os selects do painel de unidades
// Importados de ./components/Select.js

// Estilos comuns para todos os selects do painel de unidades
const unitsSelectStyles = {
  control: (base) => ({
    ...base,
    backgroundColor: 'transparent',
    border: 'none',
    boxShadow: 'none'
  }),
  singleValue: (base) => ({
    ...base,
    color: 'white'
  }),
  menu: (base) => ({
    ...base,
    backgroundColor: '#000000',
    border: '1px solid rgba(76, 175, 80, 0.5)',
    borderRadius: '0 0 4px 4px',
    zIndex: 9999999,
    position: 'absolute'
  }),
  menuPortal: (base) => ({
    ...base,
    zIndex: 9999999
  }),
  option: (base, state) => ({
    ...base,
    backgroundColor: state.isSelected ? 'rgba(76, 175, 80, 0.2)' :
                     state.isFocused ? 'rgba(76, 175, 80, 0.1)' : 'transparent',
    color: 'white'
  })
};

// Constantes

// Function to remove unnecessary zeros
const removeTrailingZeros = (numberStr) => {
  if (!numberStr) return '';

  // Converte para string se for número
  const strValue = typeof numberStr === 'number' ? numberStr.toString() : numberStr;

  // Remove zeros à direita após o ponto decimal
  return strValue.replace(/(\.\d*?[1-9])0+$/, '$1').replace(/\.0+$/, '');
};

// Função para formatar valores em notação científica no formato Y × 10^X
const formatScientific = (value) => {
  return formatScientificNotation(value);
};

function Chemistry() {
  const parametersRef = useRef(null);
  // eslint-disable-next-line no-unused-vars
  const [parametersHeight, setParametersHeight] = useState(0);
  const [composto, setComposto] = useState('');
  const [massaMolar, setMassaMolar] = useState(null);
  const [inputValue, setInputValue] = useState('');
  const [fromUnit, setFromUnit] = useState('mass');
  const [toUnit, setToUnit] = useState('moles');
  const [result, setResult] = useState(null);
  /* eslint-disable no-unused-vars */
  const [pressure, setPressure] = useState(1); // Usado no componente GasLaws
  const [temperature, setTemperature] = useState(273.15); // Usado no componente GasLaws
  const [selectedPreset, setSelectedPreset] = useState('custom');
  const [activeTab, setActiveTab] = useState('conversions');
  const [activeGasLaw, setActiveGasLaw] = useState('ideal');

  // Estados específicos para Gas Laws
  const [lastIdealGasField, setLastIdealGasField] = useState('');
  const [isTypingIdealGas, setIsTypingIdealGas] = useState(false);
  const [pressure1, setPressure1] = useState('');
  const [volume1, setVolume1] = useState('');
  const [pressure2, setPressure2] = useState('');
  const [volume2, setVolume2] = useState('');
  const [lastBoyleField, setLastBoyleField] = useState('');
  const [isTypingBoyle, setIsTypingBoyle] = useState(false);
  const [volume1Charles, setVolume1Charles] = useState('');
  const [temperature1, setTemperature1] = useState('');
  const [volume2Charles, setVolume2Charles] = useState('');
  const [temperature2, setTemperature2] = useState('');
  const [lastCharlesField, setLastCharlesField] = useState('');
  const [isTypingCharles, setIsTypingCharles] = useState(false);

  // Estados para resultados dos cálculos
  const [gasLawResult, setGasLawResult] = useState('');
  const [gasLawError, setGasLawError] = useState('');
  /* eslint-enable no-unused-vars */
  const [isRotating, setIsRotating] = useState(false);

  // Estados para elementos e erros
  const [elementosInfo, setElementosInfo] = useState([]);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  // Estados para unidades e valores
  const [massUnit, setMassUnit] = useState('g');
  const [massValue, setMassValue] = useState('');
  const [volumeUnit, setVolumeUnit] = useState('L');
  const [volumeValue, setVolumeValue] = useState('');
  const [quantityUnit, setQuantityUnit] = useState('mol');
  const [quantityValue, setQuantityValue] = useState('');
  const [concentrationUnit, setConcentrationUnit] = useState('M');
  const [temperatureUnit, setTemperatureUnit] = useState('K');
  const [pressureUnit, setPressureUnit] = useState('atm');
  /* eslint-disable no-unused-vars */
  const [unitUpdateTrigger, setUnitUpdateTrigger] = useState(0);
  const [originalValueInGrams, setOriginalValueInGrams] = useState(null);
  /* eslint-enable no-unused-vars */

  // Funções
  const handleConvert = useCallback(() => {
    // Verificar se o campo de entrada está vazio
    if (!inputValue || !inputValue.trim()) {
      setResult('');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Extrair o valor numérico diretamente usando parseFloat
      const value = parseFloat(inputValue);

      if (isNaN(value)) {
        setError('Por favor, insira um valor numérico válido.');
        setIsLoading(false);
        return;
      }

      let convertedValue;

      // Forçar a atualização do valor correspondente ao fromUnit
      if (fromUnit === 'mass') {
        // Se estamos convertendo de massa, garantir que massValue está atualizado
        if (massValue !== value.toString()) {
          setMassValue(value.toString());
        }
      } else if (fromUnit === 'moles') {
        // Se estamos convertendo de moles, garantir que quantityValue está atualizado
        if (quantityValue !== value.toString()) {
          setQuantityValue(value.toString());
        }
      } else if (fromUnit === 'volume') {
        // Se estamos convertendo de volume, garantir que volumeValue está atualizado
        if (volumeValue !== value.toString()) {
          setVolumeValue(value.toString());
        }
      }

      // Realizar a conversão com base nas unidades selecionadas
      if (fromUnit === 'mass' && toUnit === 'moles') {
        if (!massaMolar) {
          setError('Por favor, insira um composto válido para calcular a massa molar.');
          setIsLoading(false);
          return;
        }

        // Verificar se a massa molar é válida
        if (massaMolar <= 0) {
          setError(`Massa molar inválida: ${massaMolar}`);
          setIsLoading(false);
          return;
        }

        // Usar o valor de massValue se disponível, caso contrário usar o valor do input
        const massaValue = massValue ? parseFloat(massValue) : value;

        // Converter o valor da unidade atual para gramas (unidade base)
        const massaValueInGrams = convertMass(massaValue, massUnit, 'g');

        convertedValue = gramsToMoles(massaValueInGrams, massaMolar);
      } else if (fromUnit === 'moles' && toUnit === 'mass') {
        if (!massaMolar) {
          setError('Por favor, insira um composto válido para calcular a massa molar.');
          setIsLoading(false);
          return;
        }

        // Verificar se a massa molar é válida
        if (massaMolar <= 0) {
          setError(`Massa molar inválida: ${massaMolar}`);
          setIsLoading(false);
          return;
        }

        // Usar o valor de quantityValue se disponível, caso contrário usar o valor do input
        const molesValue = quantityValue ? parseFloat(quantityValue) : value;

        // Converter o valor da unidade atual para mols (unidade base)
        const molesValueInMol = convertQuantity(molesValue, quantityUnit, 'mol');

        // Calcular em gramas e depois converter para a unidade de massa atual
        const resultInGrams = molesToGrams(molesValueInMol, massaMolar);
        convertedValue = convertMass(resultInGrams, 'g', massUnit);
      } else if (fromUnit === 'moles' && toUnit === 'atoms') {
        // Usar o valor de quantityValue se disponível, caso contrário usar o valor do input
        const molesValue = quantityValue ? parseFloat(quantityValue) : value;

        // Converter o valor da unidade atual para mols (unidade base)
        const molesValueInMol = convertQuantity(molesValue, quantityUnit, 'mol');

        convertedValue = molesToAtoms(molesValueInMol);
      } else if (fromUnit === 'atoms' && toUnit === 'moles') {
        // Calcular em mols e depois converter para a unidade de quantidade atual
        const resultInMol = atomsToMoles(value);
        convertedValue = convertQuantity(resultInMol, 'mol', quantityUnit);
      } else if (fromUnit === 'moles' && toUnit === 'volume') {
        // Usar o valor de quantityValue se disponível, caso contrário usar o valor do input
        const molesValue = quantityValue ? parseFloat(quantityValue) : value;

        // Converter o valor da unidade atual para mols (unidade base)
        const molesValueInMol = convertQuantity(molesValue, quantityUnit, 'mol');

        // Calcular em litros e depois converter para a unidade de volume atual
        const resultInLiters = molesToVolume(molesValueInMol, temperature, pressure);
        convertedValue = convertVolume(resultInLiters, 'L', volumeUnit);
      } else if (fromUnit === 'volume' && toUnit === 'moles') {
        // Usar o valor de volumeValue se disponível, caso contrário usar o valor do input
        const volValue = volumeValue ? parseFloat(volumeValue) : value;

        // Converter o valor da unidade atual para litros (unidade base)
        const volValueInLiters = convertVolume(volValue, volumeUnit, 'L');

        // Calcular em mols e depois converter para a unidade de quantidade atual
        const resultInMol = volumeToMoles(volValueInLiters, temperature, pressure);
        convertedValue = convertQuantity(resultInMol, 'mol', quantityUnit);
      } else {
        setError('Conversão não suportada.');
        setIsLoading(false);
        return;
      }

      // Formatar o resultado para exibição
      if (typeof convertedValue === 'number') {
        // Verificar se o resultado é muito pequeno e precisa de notação científica
        if (Math.abs(convertedValue) < 0.0001) {
          setResult(formatScientific(convertedValue));
        } else {
          // Limitar a 6 casas decimais para números normais
          setResult(convertedValue.toFixed(6).replace(/\.?0+$/, ''));
        }
      } else {
        setResult(convertedValue);
      }
    } catch (err) {
      setError(`Erro na conversão: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [fromUnit, toUnit, inputValue, massaMolar, massValue, volumeValue, quantityValue]);

  const handleSwapUnits = () => {
    // Trocar as unidades de origem e destino
    const temp = fromUnit;
    setFromUnit(toUnit);
    setToUnit(temp);

    // Iniciar a animação de rotação
    setIsRotating(true);
    setTimeout(() => {
      setIsRotating(false);

      // Realizar a conversão automaticamente após a troca
      if (inputValue) {
        const value = parseFloat(inputValue);
        if (!isNaN(value)) {
          handleConvert();
        }
      }
    }, 500);
  };

  const getSpecificUnit = (unitType) => {
    switch (unitType) {
      case 'temperature':
        return temperatureUnit;
      case 'pressure':
        return pressureUnit;
      case 'mass':
        return massUnit;
      case 'volume':
        return volumeUnit;
      case 'quantity':
        return quantityUnit;
      case 'concentration':
        return concentrationUnit;
      default:
        return '';
    }
  };

  // Funções para Gas Laws
  const calculateIdealGasLaw = useCallback(() => {
    try {
      setGasLawError('');

      // Converter valores para números, tratando strings vazias como NaN
      const n = quantityValue === '' ? NaN : parseFloat(quantityValue);
      const V = volumeValue === '' ? NaN : parseFloat(volumeValue);
      const P = pressure === '' ? NaN : parseFloat(pressure);
      const T = temperature === '' ? NaN : parseFloat(temperature);
      const R = 0.08206; // L·atm/(mol·K)

      // Contar quantos valores válidos foram fornecidos
      const validValues = [n, V, P, T].filter(val => !isNaN(val) && val > 0);

      if (validValues.length !== 3) {
        setGasLawError('Please provide exactly 3 values to calculate the 4th');
        setGasLawResult('');
        return;
      }

      let result = '';
      let calculatedValue = 0;

      // Determinar qual valor calcular baseado em qual está vazio ou foi o último editado
      if (isNaN(n)) {
        // Calcular n: n = PV/(RT)
        if (isNaN(V) || isNaN(P) || isNaN(T)) {
          setGasLawError('Cannot calculate: missing required values for n = PV/(RT)');
          return;
        }
        calculatedValue = (P * V) / (R * T);
        result = `Moles (n) = ${calculatedValue.toFixed(4)} ${quantityUnit}`;
        setQuantityValue(calculatedValue.toString());
      } else if (isNaN(V)) {
        // Calcular V: V = nRT/P
        if (isNaN(n) || isNaN(P) || isNaN(T)) {
          setGasLawError('Cannot calculate: missing required values for V = nRT/P');
          return;
        }
        calculatedValue = (n * R * T) / P;
        result = `Volume (V) = ${calculatedValue.toFixed(4)} ${volumeUnit}`;
        setVolumeValue(calculatedValue.toString());
      } else if (isNaN(P)) {
        // Calcular P: P = nRT/V
        if (isNaN(n) || isNaN(V) || isNaN(T)) {
          setGasLawError('Cannot calculate: missing required values for P = nRT/V');
          return;
        }
        calculatedValue = (n * R * T) / V;
        result = `Pressure (P) = ${calculatedValue.toFixed(4)} ${pressureUnit}`;
        setPressure(calculatedValue.toString());
      } else if (isNaN(T)) {
        // Calcular T: T = PV/(nR)
        if (isNaN(n) || isNaN(V) || isNaN(P)) {
          setGasLawError('Cannot calculate: missing required values for T = PV/(nR)');
          return;
        }
        calculatedValue = (P * V) / (n * R);
        result = `Temperature (T) = ${calculatedValue.toFixed(4)} ${temperatureUnit}`;
        setTemperature(calculatedValue.toString());
      } else {
        // Todos os valores estão preenchidos, usar o último campo editado
        if (lastIdealGasField === 'quantity') {
          calculatedValue = (P * V) / (R * T);
          result = `Moles (n) = ${calculatedValue.toFixed(4)} ${quantityUnit}`;
          setQuantityValue(calculatedValue.toString());
        } else if (lastIdealGasField === 'volume') {
          calculatedValue = (n * R * T) / P;
          result = `Volume (V) = ${calculatedValue.toFixed(4)} ${volumeUnit}`;
          setVolumeValue(calculatedValue.toString());
        } else if (lastIdealGasField === 'pressure') {
          calculatedValue = (n * R * T) / V;
          result = `Pressure (P) = ${calculatedValue.toFixed(4)} ${pressureUnit}`;
          setPressure(calculatedValue.toString());
        } else if (lastIdealGasField === 'temperature') {
          calculatedValue = (P * V) / (n * R);
          result = `Temperature (T) = ${calculatedValue.toFixed(4)} ${temperatureUnit}`;
          setTemperature(calculatedValue.toString());
        } else {
          setGasLawError('Please edit a field to indicate which value to calculate');
          return;
        }
      }

      setGasLawResult(result);
    } catch (error) {
      setGasLawError('Error in calculation: ' + error.message);
      setGasLawResult('');
    }
  }, [quantityValue, volumeValue, pressure, temperature, lastIdealGasField, quantityUnit, volumeUnit, pressureUnit, temperatureUnit, setQuantityValue, setVolumeValue, setPressure, setTemperature]);

  const calculateBoyleLaw = useCallback(() => {
    try {
      setGasLawError('');

      // Converter valores para números, tratando strings vazias como NaN
      const P1 = pressure1 === '' ? NaN : parseFloat(pressure1);
      const V1 = volume1 === '' ? NaN : parseFloat(volume1);
      const P2 = pressure2 === '' ? NaN : parseFloat(pressure2);
      const V2 = volume2 === '' ? NaN : parseFloat(volume2);

      // Contar quantos valores válidos foram fornecidos
      const validValues = [P1, V1, P2, V2].filter(val => !isNaN(val) && val > 0);

      if (validValues.length !== 3) {
        setGasLawError('Please provide exactly 3 values to calculate the 4th');
        setGasLawResult('');
        return;
      }

      let result = '';
      let calculatedValue = 0;

      // Determinar qual valor calcular baseado em qual está vazio
      if (isNaN(P1)) {
        // Calcular P1: P1 = P2V2/V1
        if (isNaN(P2) || isNaN(V2) || isNaN(V1)) {
          setGasLawError('Cannot calculate: missing required values for P₁ = P₂V₂/V₁');
          return;
        }
        calculatedValue = (P2 * V2) / V1;
        result = `Pressure 1 (P₁) = ${calculatedValue.toFixed(4)} ${pressureUnit}`;
        setPressure1(calculatedValue.toString());
      } else if (isNaN(V1)) {
        // Calcular V1: V1 = P2V2/P1
        if (isNaN(P2) || isNaN(V2) || isNaN(P1)) {
          setGasLawError('Cannot calculate: missing required values for V₁ = P₂V₂/P₁');
          return;
        }
        calculatedValue = (P2 * V2) / P1;
        result = `Volume 1 (V₁) = ${calculatedValue.toFixed(4)} ${volumeUnit}`;
        setVolume1(calculatedValue.toString());
      } else if (isNaN(P2)) {
        // Calcular P2: P2 = P1V1/V2
        if (isNaN(P1) || isNaN(V1) || isNaN(V2)) {
          setGasLawError('Cannot calculate: missing required values for P₂ = P₁V₁/V₂');
          return;
        }
        calculatedValue = (P1 * V1) / V2;
        result = `Pressure 2 (P₂) = ${calculatedValue.toFixed(4)} ${pressureUnit}`;
        setPressure2(calculatedValue.toString());
      } else if (isNaN(V2)) {
        // Calcular V2: V2 = P1V1/P2
        if (isNaN(P1) || isNaN(V1) || isNaN(P2)) {
          setGasLawError('Cannot calculate: missing required values for V₂ = P₁V₁/P₂');
          return;
        }
        calculatedValue = (P1 * V1) / P2;
        result = `Volume 2 (V₂) = ${calculatedValue.toFixed(4)} ${volumeUnit}`;
        setVolume2(calculatedValue.toString());
      } else {
        // Todos os valores estão preenchidos, usar o último campo editado
        if (lastBoyleField === 'p1') {
          calculatedValue = (P2 * V2) / V1;
          result = `Pressure 1 (P₁) = ${calculatedValue.toFixed(4)} ${pressureUnit}`;
          setPressure1(calculatedValue.toString());
        } else if (lastBoyleField === 'v1') {
          calculatedValue = (P2 * V2) / P1;
          result = `Volume 1 (V₁) = ${calculatedValue.toFixed(4)} ${volumeUnit}`;
          setVolume1(calculatedValue.toString());
        } else if (lastBoyleField === 'p2') {
          calculatedValue = (P1 * V1) / V2;
          result = `Pressure 2 (P₂) = ${calculatedValue.toFixed(4)} ${pressureUnit}`;
          setPressure2(calculatedValue.toString());
        } else if (lastBoyleField === 'v2') {
          calculatedValue = (P1 * V1) / P2;
          result = `Volume 2 (V₂) = ${calculatedValue.toFixed(4)} ${volumeUnit}`;
          setVolume2(calculatedValue.toString());
        } else {
          setGasLawError('Please edit a field to indicate which value to calculate');
          return;
        }
      }

      setGasLawResult(result);
    } catch (error) {
      setGasLawError('Error in calculation: ' + error.message);
      setGasLawResult('');
    }
  }, [pressure1, volume1, pressure2, volume2, lastBoyleField, pressureUnit, volumeUnit, setPressure1, setVolume1, setPressure2, setVolume2]);

  const calculateCharlesLaw = useCallback(() => {
    try {
      setGasLawError('');

      // Converter valores para números, tratando strings vazias como NaN
      const V1 = volume1Charles === '' ? NaN : parseFloat(volume1Charles);
      const T1 = temperature1 === '' ? NaN : parseFloat(temperature1);
      const V2 = volume2Charles === '' ? NaN : parseFloat(volume2Charles);
      const T2 = temperature2 === '' ? NaN : parseFloat(temperature2);

      // Contar quantos valores válidos foram fornecidos
      const validValues = [V1, T1, V2, T2].filter(val => !isNaN(val) && val > 0);

      if (validValues.length !== 3) {
        setGasLawError('Please provide exactly 3 values to calculate the 4th');
        setGasLawResult('');
        return;
      }

      let result = '';
      let calculatedValue = 0;

      // Determinar qual valor calcular baseado em qual está vazio
      if (isNaN(V1)) {
        // Calcular V1: V1 = V2T1/T2
        if (isNaN(V2) || isNaN(T1) || isNaN(T2)) {
          setGasLawError('Cannot calculate: missing required values for V₁ = V₂T₁/T₂');
          return;
        }
        calculatedValue = (V2 * T1) / T2;
        result = `Volume 1 (V₁) = ${calculatedValue.toFixed(4)} ${volumeUnit}`;
        setVolume1Charles(calculatedValue.toString());
      } else if (isNaN(T1)) {
        // Calcular T1: T1 = V1T2/V2
        if (isNaN(V1) || isNaN(T2) || isNaN(V2)) {
          setGasLawError('Cannot calculate: missing required values for T₁ = V₁T₂/V₂');
          return;
        }
        calculatedValue = (V1 * T2) / V2;
        result = `Temperature 1 (T₁) = ${calculatedValue.toFixed(4)} ${temperatureUnit}`;
        setTemperature1(calculatedValue.toString());
      } else if (isNaN(V2)) {
        // Calcular V2: V2 = V1T2/T1
        if (isNaN(V1) || isNaN(T2) || isNaN(T1)) {
          setGasLawError('Cannot calculate: missing required values for V₂ = V₁T₂/T₁');
          return;
        }
        calculatedValue = (V1 * T2) / T1;
        result = `Volume 2 (V₂) = ${calculatedValue.toFixed(4)} ${volumeUnit}`;
        setVolume2Charles(calculatedValue.toString());
      } else if (isNaN(T2)) {
        // Calcular T2: T2 = V2T1/V1
        if (isNaN(V2) || isNaN(T1) || isNaN(V1)) {
          setGasLawError('Cannot calculate: missing required values for T₂ = V₂T₁/V₁');
          return;
        }
        calculatedValue = (V2 * T1) / V1;
        result = `Temperature 2 (T₂) = ${calculatedValue.toFixed(4)} ${temperatureUnit}`;
        setTemperature2(calculatedValue.toString());
      } else {
        // Todos os valores estão preenchidos, usar o último campo editado
        if (lastCharlesField === 'v1') {
          calculatedValue = (V2 * T1) / T2;
          result = `Volume 1 (V₁) = ${calculatedValue.toFixed(4)} ${volumeUnit}`;
          setVolume1Charles(calculatedValue.toString());
        } else if (lastCharlesField === 't1') {
          calculatedValue = (V1 * T2) / V2;
          result = `Temperature 1 (T₁) = ${calculatedValue.toFixed(4)} ${temperatureUnit}`;
          setTemperature1(calculatedValue.toString());
        } else if (lastCharlesField === 'v2') {
          calculatedValue = (V1 * T2) / T1;
          result = `Volume 2 (V₂) = ${calculatedValue.toFixed(4)} ${volumeUnit}`;
          setVolume2Charles(calculatedValue.toString());
        } else if (lastCharlesField === 't2') {
          calculatedValue = (V2 * T1) / V1;
          result = `Temperature 2 (T₂) = ${calculatedValue.toFixed(4)} ${temperatureUnit}`;
          setTemperature2(calculatedValue.toString());
        } else {
          setGasLawError('Please edit a field to indicate which value to calculate');
          return;
        }
      }

      setGasLawResult(result);
    } catch (error) {
      setGasLawError('Error in calculation: ' + error.message);
      setGasLawResult('');
    }
  }, [volume1Charles, temperature1, volume2Charles, temperature2, lastCharlesField, volumeUnit, temperatureUnit, setVolume1Charles, setTemperature1, setVolume2Charles, setTemperature2]);

  const handlePresetChange = useCallback((preset) => {
    setSelectedPreset(preset);
    if (preset === 'STP') {
      setTemperature(273.15);
      setPressure(1);
    } else if (preset === 'SATP') {
      setTemperature(298.15);
      setPressure(1);
    }
  }, []);

  const checkForPresetMatch = useCallback(() => {
    if (Math.abs(temperature - 273.15) < 0.1 && Math.abs(pressure - 1) < 0.01) {
      setSelectedPreset('STP');
    } else if (Math.abs(temperature - 298.15) < 0.1 && Math.abs(pressure - 1) < 0.01) {
      setSelectedPreset('SATP');
    } else {
      setSelectedPreset('custom');
    }
  }, [temperature, pressure]);

  const getPresetButtonText = useCallback((preset) => {
    switch (preset) {
      case 'STP':
        return 'STP (0°C, 1 atm)';
      case 'SATP':
        return 'SATP (25°C, 1 atm)';
      default:
        return preset;
    }
  }, []);

  // Função para limpar resultados
  const clearGasLawResults = useCallback(() => {
    setGasLawResult('');
    setGasLawError('');
  }, []);

  // Função para converter valores quando a unidade muda
  const convertValueOnUnitChange = useCallback((value, fromUnit, toUnit, conversionType) => {
    if (!value || value === '' || fromUnit === toUnit) {
      return value;
    }

    const numValue = parseFloat(value);
    if (isNaN(numValue)) {
      return value;
    }

    try {
      let convertedValue;

      switch (conversionType) {
        case 'mass':
          convertedValue = convertMass(numValue, fromUnit, toUnit);
          break;
        case 'volume':
          convertedValue = convertVolume(numValue, fromUnit, toUnit);
          break;
        case 'quantity':
          convertedValue = convertQuantity(numValue, fromUnit, toUnit);
          break;
        default:
          return value;
      }

      // Formatar o resultado para evitar números muito longos
      if (convertedValue < 0.001 || convertedValue > 1000000) {
        return convertedValue.toExponential(3);
      } else {
        return convertedValue.toPrecision(4);
      }
    } catch (error) {
      console.error('Error converting value:', error);
      return value;
    }
  }, []);

  // Função para formatar temperatura com o símbolo correto
  const formatTemperature = (value, unit) => {
    if (!value) return ' ';
    const formattedValue = formatValue(value);
    if (unit === 'C') {
      return `${formattedValue} °C`;
    } else if (unit === 'F') {
      return `${formattedValue} °F`;
    } else {
      return `${formattedValue} K`;
    }
  };

  // Helper functions
  const formatValue = (value) => {
    return formatScientificNotation(value);
  };

  // Função para lidar com a mudança no campo de entrada
  const handleInputChange = (e) => {
    // Obter o valor digitado
    const value = e.target.value;

    // Aceitar qualquer entrada - a validação será feita na conversão
    setInputValue(value);

    // Limpar o erro se houver
    setError(null);

    // Se o campo estiver vazio, limpar o resultado
    if (!value.trim()) {
      setResult('');
    }

  };

  // Efeito para calcular a massa molar quando o composto muda
  useEffect(() => {
    if (composto) {
      try {
        setIsLoading(true);
        const { massaMolar: mm, elementos } = calcularMassaMolar(composto);

        setMassaMolar(mm);
        setElementosInfo(elementos);
        setError(null);



        // Recalcular a conversão se houver um valor de entrada
        if (inputValue && !isNaN(parseFloat(inputValue))) {
          handleConvert();
        }
      } catch (err) {
        setError(`Erro ao calcular massa molar: ${err.message}`);
        setMassaMolar(null);
        setElementosInfo([]);
      } finally {
        setIsLoading(false);
      }
    } else {
      setMassaMolar(null);
      setElementosInfo([]);
    }
  }, [composto, handleConvert, inputValue]);

  // Efeito para recalcular a conversão quando a massa molar muda
  useEffect(() => {
    // Recalcular a conversão se houver um valor de entrada e uma massa molar válida
    if (massaMolar && inputValue && !isNaN(parseFloat(inputValue))) {
      handleConvert();
    }
  }, [massaMolar, handleConvert, inputValue]);

  // Efeito para medir a altura do painel de parâmetros e definir uma variável CSS
  useEffect(() => {
    const updateHeight = () => {
      if (parametersRef.current) {
        const height = parametersRef.current.offsetHeight;
        document.documentElement.style.setProperty('--parameters-height', `${height}px`);
        setParametersHeight(height);
      }
    };

    // Atualiza a altura inicialmente
    updateHeight();

    // Atualiza a altura quando o conteúdo mudar
    const observer = new ResizeObserver(updateHeight);
    if (parametersRef.current) {
      observer.observe(parametersRef.current);
    }

    // Limpa o observer quando o componente for desmontado
    return () => {
      if (parametersRef.current) {
        observer.unobserve(parametersRef.current);
      }
    };
  }, []);
  const [showPeriodicTable, setShowPeriodicTable] = useState(false);
  const [showCommonIonsTable, setShowCommonIonsTable] = useState(false);

  const items = [
    { icon: <span role='img' aria-label='Periodic Table'>🧪</span>, label: 'periodic table', onClick: () => setShowPeriodicTable(true) },
    { icon: <VscArchive size={18} />, label: 'Ions', onClick: () => setShowCommonIonsTable(true) },
    { icon: <VscHome size={18} />, label: 'Home', onClick: () => alert('Home!') },
    { icon: <VscAccount size={18} />, label: 'Profile', onClick: () => alert('Profile!') },
    { icon: <VscSettingsGear size={18} />, label: 'Settings', onClick: () => alert('Settings!') },
  ];

  return (
    <div className="App">
      {showPeriodicTable && (
        <PeriodicTable onClose={() => setShowPeriodicTable(false)} />
      )}
      {showCommonIonsTable && (
        <IonsTable onClose={() => setShowCommonIonsTable(false)} />
      )}
      <div className="panel parameters-panel" ref={parametersRef}>
          <div className="parameter-item">
            <span className="parameter-label">Compound:</span>
            <span className="parameter-value">{composto || ' '}</span>
          </div>
          <div className="parameter-item">
            <span className="parameter-label">Molar Mass:</span>
            <span className="parameter-value">
              {massaMolar ? `${formatValue(massaMolar)} g/mol` : ' '}
            </span>
          </div>
          <div className="parameter-item">
            <span className="parameter-label">Mass:</span>
            <span className="parameter-value">
              {massValue ? `${massValue} ${massUnit}` : ' '}
            </span>
          </div>
          <div className="parameter-item">
            <span className="parameter-label">Volume:</span>
            <span className="parameter-value">
              {volumeValue ? `${volumeValue} ${volumeUnit}` : ' '}
            </span>
          </div>
          <div className="parameter-item">
            <span className="parameter-label">Quantity:</span>
            <span className="parameter-value">
              {quantityValue ? `${quantityValue} ${quantityUnit}` : ' '}
            </span>
          </div>
          <div className="parameter-item">
            <span className="parameter-label">Temperature:</span>
            <span className="parameter-value">
              {formatTemperature(temperature, temperatureUnit)}
            </span>
          </div>
          <div className="parameter-item">
            <span className="parameter-label">Pressure:</span>
            <span className="parameter-value">
              {`${formatValue(pressure)} ${pressureUnit}`}
            </span>
          </div>
      </div>

      <div className="panel units-panel">
          <div className="parameter-item">
            <span className="parameter-label">Mass:</span>
            <span className="parameter-value">

              <Select
                value={unitOptions.mass.find(option => option.value === massUnit)}
                onChange={(option) => {
                  const oldUnit = massUnit;
                  const newUnit = option.value;

                  // Converter valores existentes
                  if (massValue) {
                    const convertedValue = convertValueOnUnitChange(massValue, oldUnit, newUnit, 'mass');
                    setMassValue(convertedValue);

                    // Se o fromUnit no Conversions for 'mass', atualizar o inputValue também
                    if (fromUnit === 'mass') {
                      setInputValue(convertedValue);
                    }
                  }

                  setMassUnit(newUnit);
                }}
                options={unitOptions.mass.map(opt => ({
                  value: opt.value,
                  label: `${opt.label.split(' ')[0]} (${opt.value})`
                }))}
                components={{
                  Control: CustomControl,
                  SingleValue: CustomSingleValue,
                  ValueContainer: CustomValueContainer,
                  DropdownIndicator: CustomDropdownIndicator,
                  Menu: CustomMenu,
                  MenuList: CustomMenuList,
                  Option: CustomOption,
                  IndicatorSeparator: () => null
                }}
                isSearchable={false}
                menuPortalTarget={document.body}
                menuPlacement="bottom"
                className="units-select mass-select"
                styles={{
                  control: (base) => ({
                    ...base,
                    backgroundColor: 'transparent',
                    border: 'none',
                    boxShadow: 'none'
                  }),
                  singleValue: (base) => ({
                    ...base,
                    color: 'white'
                  }),
                  menu: (base) => ({
                    ...base,
                    backgroundColor: '#000000',
                    border: '1px solid rgba(76, 175, 80, 0.5)',
                    borderRadius: '0 0 4px 4px',
                    zIndex: 9999999,
                    position: 'absolute',
                    marginTop: '0px'
                  }),
                  menuPortal: (base) => ({
                    ...base,
                    zIndex: 9999999
                  }),

                  option: (base, state) => ({
                    ...base,
                    backgroundColor: state.isSelected ? 'rgba(76, 175, 80, 0.2)' :
                                     state.isFocused ? 'rgba(76, 175, 80, 0.1)' : 'transparent',
                    color: 'white'
                  })
                }}
              />
            </span>
          </div>
          <div className="parameter-item">
            <span className="parameter-label">Volume:</span>
            <span className="parameter-value">
              <Select
                value={unitOptions.volume.find(option => option.value === volumeUnit)}
                onChange={(option) => {
                  const oldUnit = volumeUnit;
                  const newUnit = option.value;

                  // Converter valores existentes
                  if (volumeValue) {
                    const convertedValue = convertValueOnUnitChange(volumeValue, oldUnit, newUnit, 'volume');
                    setVolumeValue(convertedValue);

                    // Se o fromUnit no Conversions for 'volume', atualizar o inputValue também
                    if (fromUnit === 'volume') {
                      setInputValue(convertedValue);
                    }
                  }

                  setVolumeUnit(newUnit);
                }}
                options={unitOptions.volume.map(opt => ({
                  value: opt.value,
                  label: `${opt.label.split(' ')[0]} (${opt.value})`
                }))}
                components={{
                  Control: CustomControl,
                  SingleValue: CustomSingleValue,
                  ValueContainer: CustomValueContainer,
                  DropdownIndicator: CustomDropdownIndicator,
                  Menu: CustomMenu,
                  MenuList: CustomMenuList,
                  Option: CustomOption,
                  IndicatorSeparator: () => null
                }}
                isSearchable={false}
                menuPortalTarget={document.body}
                menuPlacement="bottom"
                className="units-select volume-select"
                styles={{
                  control: (base) => ({
                    ...base,
                    backgroundColor: 'transparent',
                    border: 'none',
                    boxShadow: 'none'
                  }),
                  singleValue: (base) => ({
                    ...base,
                    color: 'white'
                  }),
                  menu: (base) => ({
                    ...base,
                    backgroundColor: '#000000',
                    border: '1px solid rgba(76, 175, 80, 0.5)',
                    borderRadius: '0 0 4px 4px',
                    zIndex: 9999999,
                    position: 'absolute',
                    marginTop: '0px'
                  }),
                  menuPortal: (base) => ({
                    ...base,
                    zIndex: 9999999
                  }),

                  option: (base, state) => ({
                    ...base,
                    backgroundColor: state.isSelected ? 'rgba(76, 175, 80, 0.2)' :
                                     state.isFocused ? 'rgba(76, 175, 80, 0.1)' : 'transparent',
                    color: 'white'
                  })
                }}
              />
            </span>
          </div>
          <div className="parameter-item">
            <span className="parameter-label">Quantity:</span>
            <span className="parameter-value">
              <Select
                value={unitOptions.quantity ? unitOptions.quantity.find(option => option.value === quantityUnit) : { value: 'mol', label: 'Mols (mol)' }}
                onChange={(option) => {
                  const oldUnit = quantityUnit;
                  const newUnit = option.value;

                  // Converter valores existentes
                  if (quantityValue) {
                    const convertedValue = convertValueOnUnitChange(quantityValue, oldUnit, newUnit, 'quantity');
                    setQuantityValue(convertedValue);

                    // Se o fromUnit no Conversions for 'moles', atualizar o inputValue também
                    if (fromUnit === 'moles') {
                      setInputValue(convertedValue);
                    }
                  }

                  setQuantityUnit(newUnit);
                }}
                options={[
                  { value: 'mol', label: 'Mols (mol)' },
                  { value: 'mmol', label: 'Milimols (mmol)' },
                  { value: 'µmol', label: 'Micromols (µmol)' }
                ]}
                components={{
                  Control: CustomControl,
                  SingleValue: CustomSingleValue,
                  ValueContainer: CustomValueContainer,
                  DropdownIndicator: CustomDropdownIndicator,
                  Menu: CustomMenu,
                  MenuList: CustomMenuList,
                  Option: CustomOption,
                  IndicatorSeparator: () => null
                }}
                isSearchable={false}
                menuPortalTarget={document.body}
                menuPlacement="bottom"
                className="units-select quantity-select"
                styles={{
                  control: (base) => ({
                    ...base,
                    backgroundColor: 'transparent',
                    border: 'none',
                    boxShadow: 'none'
                  }),
                  singleValue: (base) => ({
                    ...base,
                    color: 'white'
                  }),
                  menu: (base) => ({
                    ...base,
                    backgroundColor: '#000000',
                    border: '1px solid rgba(76, 175, 80, 0.5)',
                    borderRadius: '0 0 4px 4px',
                    zIndex: 9999999,
                    position: 'absolute',
                    marginTop: '0px'
                  }),
                  menuPortal: (base) => ({
                    ...base,
                    zIndex: 9999999
                  }),

                  option: (base, state) => ({
                    ...base,
                    backgroundColor: state.isSelected ? 'rgba(76, 175, 80, 0.2)' :
                                     state.isFocused ? 'rgba(76, 175, 80, 0.1)' : 'transparent',
                    color: 'white'
                  })
                }}
              />
            </span>
          </div>
          <div className="parameter-item">
            <span className="parameter-label">Temperature:</span>
            <span className="parameter-value">
              <Select
                value={unitOptions.temperature.find(option => option.value === temperatureUnit)}
                onChange={(option) => setTemperatureUnit(option.value)}
                options={unitOptions.temperature.map(opt => ({
                  value: opt.value,
                  label: `${opt.label.split(' ')[0]} (${opt.value})`
                }))}
                components={{
                  Control: CustomControl,
                  SingleValue: CustomSingleValue,
                  ValueContainer: CustomValueContainer,
                  DropdownIndicator: CustomDropdownIndicator,
                  Menu: CustomMenu,
                  MenuList: CustomMenuList,
                  Option: CustomOption,
                  IndicatorSeparator: () => null
                }}
                isSearchable={false}
                menuPortalTarget={document.body}
                menuPlacement="bottom"
                className="units-select temperature-select"
                styles={{
                  control: (base) => ({
                    ...base,
                    backgroundColor: 'transparent',
                    border: 'none',
                    boxShadow: 'none'
                  }),
                  singleValue: (base) => ({
                    ...base,
                    color: 'white'
                  }),
                  menu: (base) => ({
                    ...base,
                    backgroundColor: '#000000',
                    border: '1px solid rgba(76, 175, 80, 0.5)',
                    borderRadius: '0 0 4px 4px',
                    zIndex: 9999999,
                    position: 'absolute',
                    marginTop: '0px'
                  }),
                  menuPortal: (base) => ({
                    ...base,
                    zIndex: 9999999
                  }),

                  option: (base, state) => ({
                    ...base,
                    backgroundColor: state.isSelected ? 'rgba(76, 175, 80, 0.2)' :
                                     state.isFocused ? 'rgba(76, 175, 80, 0.1)' : 'transparent',
                    color: 'white'
                  })
                }}
              />
            </span>
          </div>
          <div className="parameter-item">
            <span className="parameter-label">Pressure:</span>
            <span className="parameter-value">
              <Select
                value={unitOptions.pressure.find(option => option.value === pressureUnit)}
                onChange={(option) => setPressureUnit(option.value)}
                options={unitOptions.pressure.map(opt => ({
                  value: opt.value,
                  label: `${opt.label.split(' ')[0]} (${opt.value})`
                }))}
                components={{
                  Control: CustomControl,
                  SingleValue: CustomSingleValue,
                  ValueContainer: CustomValueContainer,
                  DropdownIndicator: CustomDropdownIndicator,
                  Menu: CustomMenu,
                  MenuList: CustomMenuList,
                  Option: CustomOption,
                  IndicatorSeparator: () => null
                }}
                isSearchable={false}
                menuPortalTarget={document.body}
                menuPlacement="bottom"
                className="units-select pressure-select"
                styles={{
                  control: (base) => ({
                    ...base,
                    backgroundColor: 'transparent',
                    border: 'none',
                    boxShadow: 'none'
                  }),
                  singleValue: (base) => ({
                    ...base,
                    color: 'white'
                  }),
                  menu: (base) => ({
                    ...base,
                    backgroundColor: '#000000',
                    border: '1px solid rgba(76, 175, 80, 0.5)',
                    borderRadius: '0 0 4px 4px',
                    zIndex: 9999999,
                    position: 'absolute',
                    marginTop: '0px'
                  }),
                  menuPortal: (base) => ({
                    ...base,
                    zIndex: 9999999
                  }),

                  option: (base, state) => ({
                    ...base,
                    backgroundColor: state.isSelected ? 'rgba(76, 175, 80, 0.2)' :
                                     state.isFocused ? 'rgba(76, 175, 80, 0.1)' : 'transparent',
                    color: 'white'
                  })
                }}
              />
            </span>
          </div>
          <div className="parameter-item">
            <span className="parameter-label">Concentration:</span>
            <span className="parameter-value">
              <Select
                value={unitOptions.concentration.find(option => option.value === concentrationUnit)}
                onChange={(option) => setConcentrationUnit(option.value)}
                options={unitOptions.concentration.map(opt => ({
                  value: opt.value,
                  label: `${opt.label.split(' ')[0]} (${opt.value})`
                }))}
                components={{
                  Control: CustomControl,
                  SingleValue: CustomSingleValue,
                  ValueContainer: CustomValueContainer,
                  DropdownIndicator: CustomDropdownIndicator,
                  Menu: CustomMenu,
                  MenuList: CustomMenuList,
                  Option: CustomOption,
                  IndicatorSeparator: () => null
                }}
                isSearchable={false}
                menuPortalTarget={document.body}
                menuPlacement="bottom"
                className="units-select concentration-select"
                styles={{
                  control: (base) => ({
                    ...base,
                    backgroundColor: 'transparent',
                    border: 'none',
                    boxShadow: 'none'
                  }),
                  singleValue: (base) => ({
                    ...base,
                    color: 'white'
                  }),
                  menu: (base) => ({
                    ...base,
                    backgroundColor: '#000000',
                    border: '1px solid rgba(76, 175, 80, 0.5)',
                    borderRadius: '0 0 4px 4px',
                    zIndex: 9999999,
                    position: 'absolute',
                    marginTop: '0px'
                  }),
                  menuPortal: (base) => ({
                    ...base,
                    zIndex: 9999999
                  }),

                  option: (base, state) => ({
                    ...base,
                    backgroundColor: state.isSelected ? 'rgba(76, 175, 80, 0.2)' :
                                     state.isFocused ? 'rgba(76, 175, 80, 0.1)' : 'transparent',
                    color: 'white'
                  })
                }}
              />
            </span>
          </div>
      </div>

      <div className="main-content-wrapper">
        <Squares
          speed={0.3}
          squareSize={30}
          direction="diagonal"
          borderColor="#222"
          hoverFillColor="#222"
          className="background-squares"
        />
        <div className="content-container">
          <h1>Chemistry Calculator</h1>

          <div className="calculator-block calculo-container">
            <h2>Compound</h2>
            <div className="input-group">
              <input
                type="text"
                value={composto}
                onChange={(e) => setComposto(e.target.value)}
                placeholder="Enter chemical formula"
                className="chemical-input"
              />
            </div>

            {composto && (
              <CompoundCarousel
                composto={composto}
                massaMolar={massaMolar}
                elementosInfo={elementosInfo}
                formatValue={formatValue}
                removeTrailingZeros={removeTrailingZeros}
              />
            )}
          </div>

          <SimpleCarousel
            slides={[
              {
                id: 'conversions',
                title: 'Conversions',
                component: (
                  <Conversions
                    unitOptions={unitOptions}
                    fromUnit={fromUnit}
                    setFromUnit={setFromUnit}
                    toUnit={toUnit}
                    setToUnit={setToUnit}
                    inputValue={inputValue}
                    handleInputChange={handleInputChange}
                    result={result}
                    error={error}
                    isLoading={isLoading}
                    handleConvert={handleConvert}
                    handleSwapUnits={handleSwapUnits}
                    isRotating={isRotating}
                    setInputValue={setInputValue}
                    setUnitUpdateTrigger={setUnitUpdateTrigger}
                    getSpecificUnit={getSpecificUnit}
                    setMassValue={setMassValue}
                    setVolumeValue={setVolumeValue}
                    setQuantityValue={setQuantityValue}
                    massUnit={massUnit}
                    volumeUnit={volumeUnit}
                    quantityUnit={quantityUnit}
                    massValue={massValue}
                    volumeValue={volumeValue}
                    quantityValue={quantityValue}
                  />
                )
              },
              {
                id: 'gas-laws',
                title: 'Gas Laws',
                component: (
                  <GasLaws
                    activeGasLaw={activeGasLaw}
                    setActiveGasLaw={setActiveGasLaw}
                    R_IDEAL_GAS_CONSTANT={0.08206}
                    quantityValue={quantityValue}
                    setQuantityValue={setQuantityValue}
                    volumeValue={volumeValue}
                    setVolumeValue={setVolumeValue}
                    pressure={pressure}
                    setPressure={setPressure}
                    temperature={temperature}
                    setTemperature={setTemperature}
                    quantityUnit={quantityUnit}
                    volumeUnit={volumeUnit}
                    pressureUnit={pressureUnit}
                    temperatureUnit={temperatureUnit}
                    setLastIdealGasField={setLastIdealGasField}
                    setIsTypingIdealGas={setIsTypingIdealGas}
                    calculateIdealGasLaw={calculateIdealGasLaw}
                    selectedPreset={selectedPreset}
                    handlePresetChange={handlePresetChange}
                    getPresetButtonText={getPresetButtonText}
                    setSelectedPreset={setSelectedPreset}
                    checkForPresetMatch={checkForPresetMatch}
                    pressure1={pressure1}
                    setPressure1={setPressure1}
                    volume1={volume1}
                    setVolume1={setVolume1}
                    pressure2={pressure2}
                    setPressure2={setPressure2}
                    volume2={volume2}
                    setVolume2={setVolume2}
                    setLastBoyleField={setLastBoyleField}
                    setIsTypingBoyle={setIsTypingBoyle}
                    calculateBoyleLaw={calculateBoyleLaw}
                    volume1Charles={volume1Charles}
                    setVolume1Charles={setVolume1Charles}
                    temperature1={temperature1}
                    setTemperature1={setTemperature1}
                    volume2Charles={volume2Charles}
                    setVolume2Charles={setVolume2Charles}
                    temperature2={temperature2}
                    setTemperature2={setTemperature2}
                    setLastCharlesField={setLastCharlesField}
                    setIsTypingCharles={setIsTypingCharles}
                    calculateCharlesLaw={calculateCharlesLaw}
                    gasLawResult={gasLawResult}
                    gasLawError={gasLawError}
                    clearGasLawResults={clearGasLawResults}
                  />
                )
              },
              {
                id: 'chemical-equations',
                title: 'Chemical Equations',
                component: (
                  <ChemicalEquationsOp
                    unitOptions={unitOptions}
                    formatValue={formatValue}
                  />
                )
              },
              {
                id: 'chemistry-tools',
                title: 'Chemistry Tools',
                component: (
                  <ChemistryToolsOp
                    unitOptions={unitOptions}
                    formatValue={formatValue}
                  />
                )
              }
            ]}
          />

          <Dock
            items={items}
            panelHeight={70}
            baseItemSize={50}
            magnification={70}
          />

          <DebugMode />
        </div>
      </div>
    </div>
  );
}

export default Chemistry;
