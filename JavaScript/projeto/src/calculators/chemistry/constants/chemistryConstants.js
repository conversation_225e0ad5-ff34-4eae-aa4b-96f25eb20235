export const diatomicElements = ['H', 'N', 'O', 'F', 'Cl', 'Br', 'I'];

// Constantes químicas
export const R_IDEAL_GAS_CONSTANT = 0.0821; // L·atm/(mol·K)
export const AVOGADROS_NUMBER = 6.022e23;

// Presets de condições
export const CONDITIONS_PRESETS = {
  STP: { temperature: 273.15, pressure: 1 }, // 0°C, 1 atm
  SATP: { temperature: 298.15, pressure: 1 }, // 25°C, 1 atm
};

// Opções de unidades
export const unitOptions = {
  temperature: [
    { value: 'K', label: 'Kelvin (K)' },
    { value: 'C', label: 'Celsius (°C)' },
    { value: 'F', label: 'Fahrenheit (F)' }
  ],
  pressure: [
    { value: 'atm', label: 'Atmosphere (atm)' },
    { value: 'mmHg', label: 'Millimeters of mercury (mmHg)' },
    { value: 'Pa', label: 'Pascal (Pa)' },
    { value: 'bar', label: 'Bar (bar)' }
  ],
  volume: [
    { value: 'TL', label: 'Teraliters (TL)' },
    { value: 'GL', label: 'Gigaliters (GL)' },
    { value: 'ML', label: 'Megaliters (ML)' },
    { value: 'kL', label: 'Kiloliters (kL)' },
    { value: 'hL', label: 'Hectoliters (hL)' },
    { value: 'daL', label: 'Decaliters (daL)' },
    { value: 'L', label: 'Liters (L)' },
    { value: 'dL', label: 'Deciliters (dL)' },
    { value: 'cL', label: 'Centiliters (cL)' },
    { value: 'mL', label: 'Milliliters (mL)' },
    { value: 'µL', label: 'Microliters (µL)' },
    { value: 'nL', label: 'Nanoliters (nL)' },
    { value: 'pL', label: 'Picoliters (pL)' },
    { value: 'fL', label: 'Femtoliters (fL)' },
    { value: 'aL', label: 'Attoliters (aL)' },
    { value: 'm³', label: 'Cubic meters (m³)' }
  ],
  mass: [
    { value: 'Tg', label: 'Teragrams (Tg)' },
    { value: 'Gg', label: 'Gigagrams (Gg)' },
    { value: 'Mg', label: 'Megagrams (Mg)' },
    { value: 'kg', label: 'Kilograms (kg)' },
    { value: 'hg', label: 'Hectograms (hg)' },
    { value: 'dag', label: 'Decagrams (dag)' },
    { value: 'g', label: 'Grams (g)' },
    { value: 'dg', label: 'Decigrams (dg)' },
    { value: 'cg', label: 'Centigrams (cg)' },
    { value: 'mg', label: 'Milligrams (mg)' },
    { value: 'µg', label: 'Micrograms (µg)' },
    { value: 'ng', label: 'Nanograms (ng)' },
    { value: 'pg', label: 'Picograms (pg)' },
    { value: 'fg', label: 'Femtograms (fg)' },
    { value: 'ag', label: 'Attograms (ag)' }
  ],
  quantity: [
    { value: 'mol', label: 'Mols (mol)' },
    { value: 'mmol', label: 'Milimols (mmol)' },
    { value: 'µmol', label: 'Micromols (µmol)' }
  ],
  concentration: [
    { value: 'M', label: 'Molar (M)' },
    { value: 'mM', label: 'Millimolar (mM)' },
    { value: 'g/L', label: 'Grams/Liter (g/L)' }
  ],
  conversion: [
    { value: 'mass', label: 'Mass (g)' },
    { value: 'moles', label: 'Moles (mol)' },
    { value: 'volume', label: 'Volume (L)' },
    { value: 'atoms', label: 'Atoms' }
  ]
};
export const CommonIonsData = {
  commonCations: [
    { symbol: 'Na⁺', name: 'Sodium' }, { symbol: 'K⁺', name: 'Potassium' }, { symbol: 'Ca²⁺', name: 'Calcium' },
    { symbol: 'Mg²⁺', name: 'Magnesium' }, { symbol: 'Fe²⁺', name: ['Iron(II)','Ferrous'] }, { symbol: 'Fe³⁺', name: ['Iron(III)','Ferric'] },
    { symbol: 'Cu²⁺', name: ['Copper(II)','Cupric'] }, { symbol: 'Cu⁺', name: ['Copper(I)','Cuprous'] }, { symbol: 'Ag⁺', name: 'Silver(I)' },
    { symbol: 'Zn²⁺', name: 'Zinc' },{ symbol: 'Al³⁺', name: 'Aluminum' }, { symbol: 'Ba²⁺', name: 'Barium' },
    { symbol: 'Pb²⁺', name: ['Lead(II)','Plumbous'] }, { symbol: 'Pb⁴⁺', name: ['Lead(IV)','Plumbic'] }, { symbol: 'Hg²⁺', name: ['Mercury(I)','Mercurous'] },
    { symbol: 'Hg⁺', name: ['Mercury(II)','Mercuric'] }, { symbol: 'H⁺', name: 'Hydrogen' }, { symbol: 'NH₄⁺', name: 'Ammonium' },
    { symbol: 'Li⁺', name: 'Lithium' }, { symbol: 'H₃O⁺', name: 'Hydronium' }, { symbol: 'Au⁺', name: ['Gold(I)','Aurous'] },
    { symbol: 'Au³⁺', name: ['Gold(III)','Auric'] }, { symbol: 'Pt⁴⁺', name: ['Platinum(IV)','Platinic'] }, { symbol: 'Pt²⁺', name: ['Platinum(II)','Platous'] },
    { symbol: 'Co²⁺', name: ['Cobalt(II)','Cobaltous'] }, { symbol: 'Co³⁺', name: ['Cobalt(III)','Cobaltic'] }, { symbol: 'Mn⁴⁺', name: ['Manganese(IV)','Manganous']  },
    { symbol: 'Mn²⁺', name: ['Manganese(II)','Manganous'] }, { symbol: 'Mn³⁺', name: ['Manganese(III)','Manganic'] }, { symbol: 'Ni²⁺', name: ['Nickel(II)','Nickelous'] },
    { symbol: 'Ni³⁺', name: ['Nickel(III)','Nickelic'] }, { symbol: 'Sn²⁺', name: ['Tin(II)','Stannous'] }, { symbol: 'Sn⁴⁺', name: ['Tin(IV)','Stannic'] },
    { symbol: 'Ti²⁺', name: ['Titanium(II)','Titanous'] }, { symbol: 'Ti³⁺', name: ['Titanium(III)','Titanic'] }, { symbol: 'Ti⁴⁺', name: ['Titanium(IV)','Titanous'] },
    { symbol: 'Cr²⁺', name: ['Chromium(II)','Chromous'] }, { symbol: 'Cr³⁺', name: ['Chromium(III)','Chromic'] }, { symbol: 'Cr⁶⁺', name: ['Chromium(VI)','Chromate'] },
    { symbol: 'Be²⁺', name: 'Beryllium' },
  ],
  commonAnions: [
    { symbol: 'Cl⁻', name: 'Chloride' }, { symbol: 'ClO⁻', name: 'Hypochlorite' }, { symbol: 'ClO₂⁻', name: 'Chlorite' }, { symbol: 'ClO₃⁻', name: 'Chlorate' }, { symbol: 'ClO₄⁻', name: 'Perchlorate' },
    { symbol: 'SO₄²⁻', name: 'Sulfate' }, { symbol: 'SO₃²⁻', name: 'Sulfite' }, { symbol: 'S²⁻', name: 'Sulfide' }, { symbol: 'SCN⁻', name: 'Thiocyanate' }, { symbol: 'S₂O₃²⁻', name: 'Thiosulfate' },
    { symbol: 'P³⁻', name: 'Phosphide' }, { symbol: 'PO₃³⁻', name: 'Phosphite' }, { symbol: 'PO₄³⁻', name: 'Phosphate' }, { symbol: 'H⁻', name: 'Hydride' }, { symbol: 'OH⁻', name: 'Hydroxide' },
    { symbol: 'O²⁻', name: 'Oxide' }, { symbol: 'O₂²⁻', name: 'Peroxide' }, { symbol: 'HS⁻', name: 'Hydrogensulfide' }, { symbol: 'S₂O₃²⁻', name: 'Thiosulfate' },
    { symbol: 'NO₃⁻', name: 'Nitrate' }, { symbol: 'NO₂⁻', name: 'Nitrite' }, { symbol: 'NO⁻', name: 'Nitroide' },
    { symbol: 'C₂O₄²⁻', name: 'Oxalate' }, { symbol: 'CO₃²⁻', name: 'Carbonate' }, { symbol: 'CO₂²⁻', name: 'Carbon dioxide' },
    { symbol: 'HCO₃⁻', name: 'Bicarbonate' }, { symbol: 'H₂CO₃', name: 'Carbonic acid' },
  ]
};
// dados dos elementos //
export const PeriodicTableData = {
  molarMass: {
    H: 1.008, He: 4.003, Li: 6.94, Be: 9.012, B: 10.81, C: 12.01, N: 14.01, O: 16.00, F: 19.00, Ne: 20.18,
    Na: 22.99, Mg: 24.31, Al: 26.98, Si: 28.09, P: 30.97, S: 32.06, Cl: 35.45, Ar: 39.95,
    K: 39.10, Ca: 40.08, Sc: 44.96, Ti: 47.87, V: 50.94, Cr: 52.00, Mn: 54.94, Fe: 55.85,
    Co: 58.93, Ni: 58.69, Cu: 63.55, Zn: 65.38, Ga: 69.72, Ge: 72.63, As: 74.92, Se: 78.97,
    Br: 79.90, Kr: 83.80, Rb: 85.47, Sr: 87.62, Y: 88.91, Zr: 91.22, Nb: 92.91, Mo: 95.95,
    Tc: 98.00, Ru: 101.1, Rh: 102.9, Pd: 106.4, Ag: 107.9, Cd: 112.4, In: 114.8, Sn: 118.7,
    Sb: 121.8, Te: 127.6, I: 126.9, Xe: 131.3, Cs: 132.9, Ba: 137.3, La: 138.9, Ce: 140.1,
    Pr: 140.9, Nd: 144.2, Pm: 145.0, Sm: 150.4, Eu: 152.0, Gd: 157.3, Tb: 158.9, Dy: 162.5,
    Ho: 164.9, Er: 167.3, Tm: 168.9, Yb: 173.0, Lu: 175.0, Hf: 178.5, Ta: 180.9, W: 183.8,
    Re: 186.2, Os: 190.2, Ir: 192.2, Pt: 195.1, Au: 197.0, Hg: 200.6, Tl: 204.4, Pb: 207.2,
    Bi: 209.0, Po: 209.0, At: 210.0, Rn: 222.0, Fr: 223.0, Ra: 226.0, Ac: 227.0, Th: 232.0,
    Pa: 231.0, U: 238.0, Np: 237.0, Pu: 244.0, Am: 243.0, Cm: 247.0, Bk: 247.0, Cf: 251.0,
    Es: 252.0, Fm: 257.0, Md: 258.0, No: 259.0, Lr: 262.0, Rf: 267.0, Db: 270.0, Sg: 269.0,
    Bh: 270.0, Hs: 269.0, Mt: 278.0, Ds: 281.0, Rg: 282.0, Cn: 285.0, Nh: 286.0, Fl: 289.0,
    Mc: 290.0, Lv: 293.0, Ts: 294.0, Og: 295.0
  },
  elementsName: {
    H: 'Hydrogen', He: 'Helium', Li: 'Lithium', Be: 'Beryllium', B: 'Boron', C: 'Carbon', N: 'Nitrogen', O: 'Oxygen', F: 'Fluorine', Ne: 'Neon',
    Na: 'Sodium', Mg: 'Magnesium', Al: 'Aluminium', Si: 'Silicon', P: 'Phosphorus', S: 'Sulfur', Cl: 'Chlorine', Ar: 'Argon',
    K: 'Potassium', Ca: 'Calcium', Sc: 'Scandium', Ti: 'Titanium', V: 'Vanadium', Cr: 'Chromium', Mn: 'Manganese', Fe: 'Iron',
    Co: 'Cobalt', Ni: 'Nickel', Cu: 'Copper', Zn: 'Zinc', Ga: 'Gallium', Ge: 'Germanium', As: 'Arsenic', Se: 'Selenium',
    Br: 'Bromine', Kr: 'Krypton', Rb: 'Rubidium', Sr: 'Strontium', Y: 'Yttrium', Zr: 'Zirconium', Nb: 'Niobium', Mo: 'Molybdenum',
    Tc: 'Technetium', Ru: 'Ruthenium', Rh: 'Rhodium', Pd: 'Palladium', Ag: 'Silver', Cd: 'Cadmium', In: 'Indium', Sn: 'Tin',
    Sb: 'Antimony', Te: 'Tellurium', I: 'Iodine', Xe: 'Xenon', Cs: 'Cesium', Ba: 'Barium', La: 'Lanthanum', Ce: 'Cerium',
    Pr: 'Praseodymium', Nd: 'Neodymium', Pm: 'Promethium', Sm: 'Samarium', Eu: 'Europium', Gd: 'Gadolinium', Tb: 'Terbium', Dy: 'Dysprosium',
    Ho: 'Holmium', Er: 'Erbium', Tm: 'Thulium', Yb: 'Ytterbium', Lu: 'Lutetium', Hf: 'Hafnium', Ta: 'Tantalum', W: 'Tungsten',
    Re: 'Rhenium', Os: 'Osmium', Ir: 'Iridium', Pt: 'Platinum', Au: 'Gold', Hg: 'Mercury', Tl: 'Thallium', Pb: 'Lead', Bi: 'Bismuth',
    Po: 'Polonium', At: 'Astatine', Rn: 'Radon', Fr: 'Francium', Ra: 'Radium', Ac: 'Actinium', Th: 'Thorium', Pa: 'Protactinium',
    U: 'Uranium', Np: 'Neptunium', Pu: 'Plutonium', Am: 'Americium', Cm: 'Curium', Bk: 'Berkelium', Cf: 'Californium', Es: 'Einsteinium',
    Fm: 'Fermium', Md: 'Mendelevium', No: 'Nobelium', Lr: 'Lawrencium', Rf: 'Rutherfordium', Db: 'Dubnium', Sg: 'Seaborgium',
    Bh: 'Bohrium', Hs: 'Hassium', Mt: 'Meitnerium', Ds: 'Darmstadtium', Rg: 'Roentgenium', Cn: 'Copernicium', Nh: 'Nihonium',
    Fl: 'Flerovium', Mc: 'Moscovium', Lv: 'Livermorio', Ts: 'Tennessine', Og: 'Oganesson'
  },
  elementsNumber: {
    H: 1, He: 2, Li: 3, Be: 4, B: 5, C: 6, N: 7, O: 8, F: 9, Ne: 10,
    Na: 11, Mg: 12, Al: 13, Si: 14, P: 15, S: 16, Cl: 17, Ar: 18,
    K: 19, Ca: 20, Sc: 21, Ti: 22, V: 23, Cr: 24, Mn: 25, Fe: 26,
    Co: 27, Ni: 28, Cu: 29, Zn: 30, Ga: 31, Ge: 32, As: 33, Se: 34,
    Br: 35, Kr: 36, Rb: 37, Sr: 38, Y: 39, Zr: 40, Nb: 41, Mo: 42,
    Tc: 43, Ru: 44, Rh: 45, Pd: 46, Ag: 47, Cd: 48, In: 49, Sn: 50,
    Sb: 51, Te: 52, I: 53, Xe: 54, Cs: 55, Ba: 56, La: 57, Ce: 58,
    Pr: 59, Nd: 60, Pm: 61, Sm: 62, Eu: 63, Gd: 64, Tb: 65, Dy: 66,
    Ho: 67, Er: 68, Tm: 69, Yb: 70, Lu: 71, Hf: 72, Ta: 73, W: 74,
    Re: 75, Os: 76, Ir: 77, Pt: 78, Au: 79, Hg: 80, Tl: 81, Pb: 82, Bi: 83,
    Po: 84, At: 85, Rn: 86, Fr: 87, Ra: 88, Ac: 89, Th: 90, Pa: 91,
    U: 92, Np: 93, Pu: 94, Am: 95, Cm: 96, Bk: 97, Cf: 98, Es: 99,
    Fm: 100, Md: 101, No: 102, Lr: 103, Rf: 104, Db: 105, Sg: 106,
    Bh: 107, Hs: 108, Mt: 109, Ds: 110, Rg: 111, Cn: 112, Nh: 113,
    Fl: 114, Mc: 115, Lv: 116, Ts: 117, Og: 118
  },
  elementsCategory: {
    H: 'Nonmetal', He: 'Noble gas', Li: 'Alkali metal', Be: 'Alkaline earth metal', B: 'Metalloid', C: 'Nonmetal', N: 'Nonmetal', O: 'Nonmetal', F: 'Nonmetal', Ne: 'Noble gas',
    Na: 'Alkali metal', Mg: 'Alkaline earth metal', Al: 'Metal', Si: 'Metalloid', P: 'Nonmetal', S: 'Nonmetal', Cl: 'Nonmetal', Ar: 'Noble gas',
    K: 'Alkali metal', Ca: 'Alkaline earth metal', Sc: 'Transition metal', Ti: 'Transition metal', V: 'Transition metal', Cr: 'Transition metal', Mn: 'Transition metal', Fe: 'Transition metal',
    Co: 'Transition metal', Ni: 'Transition metal', Cu: 'Transition metal', Zn: 'Transition metal', Ga: 'Metal', Ge: 'Metalloid', As: 'Metalloid', Se: 'Nonmetal', Br: 'Nonmetal', Kr: 'Noble gas',
    Rb: 'Alkali metal', Sr: 'Alkaline earth metal', Y: 'Transition metal', Zr: 'Transition metal', Nb: 'Transition metal', Mo: 'Transition metal', Tc: 'Transition metal', Ru: 'Transition metal',
    Rh: 'Transition metal', Pd: 'Transition metal', Ag: 'Transition metal', Cd: 'Transition metal', In: 'Metal', Sn: 'Metal', Sb: 'Metalloid', Te: 'Metalloid', I: 'Nonmetal', Xe: 'Noble gas',
    Cs: 'Alkali metal', Ba: 'Alkaline earth metal', La: 'Lanthanide', Ce: 'Lanthanide', Pr: 'Lanthanide', Nd: 'Lanthanide', Pm: 'Lanthanide', Sm: 'Lanthanide', Eu: 'Lanthanide', Gd: 'Lanthanide',
    Tb: 'Lanthanide', Dy: 'Lanthanide', Ho: 'Lanthanide', Er: 'Lanthanide', Tm: 'Lanthanide', Yb: 'Lanthanide', Lu: 'Lanthanide', Hf: 'Transition metal', Ta: 'Transition metal', W: 'Transition metal',
    Re: 'Transition metal', Os: 'Transition metal', Ir: 'Transition metal', Pt: 'Transition metal', Au: 'Transition metal', Hg: 'Transition metal', Tl: 'Metal', Pb: 'Metal', Bi: 'Metal',
    Po: 'Metalloid', At: 'Metalloid', Rn: 'Noble gas', Fr: 'Alkali metal', Ra: 'Alkaline earth metal', Ac: 'Actinide', Th: 'Actinide', Pa: 'Actinide', U: 'Actinide', Np: 'Actinide', Pu: 'Actinide',
    Am: 'Actinide', Cm: 'Actinide', Bk: 'Actinide', Cf: 'Actinide', Es: 'Actinide', Fm: 'Actinide', Md: 'Actinide', No: 'Actinide', Lr: 'Actinide', Rf: 'Transition metal', Db: 'Transition metal',
    Sg: 'Transition metal', Bh: 'Transition metal', Hs: 'Transition metal', Mt: 'Transition metal', Ds: 'Transition metal', Rg: 'Transition metal', Cn: 'Transition metal', Nh: 'Metal',
    Fl: 'Metal', Mc: 'Metal', Lv: 'Metal', Ts: 'Metalloid', Og: 'Noble gas'
  },
  electronConfiguration: {
    H: '1s¹', He: '1s²', Li: '1s² 2s¹', Be: '1s² 2s²', B: '1s² 2s² 2p¹', C: '1s² 2s² 2p²', N: '1s² 2s² 2p³', O: '1s² 2s² 2p⁴', F: '1s² 2s² 2p⁵', Ne: '1s² 2s² 2p⁶',
    Na: '1s² 2s² 2p⁶ 3s¹', Mg: '1s² 2s² 2p⁶ 3s²', Al: '1s² 2s² 2p⁶ 3s² 3p¹', Si: '1s² 2s² 2p⁶ 3s² 3p²', P: '1s² 2s² 2p⁶ 3s² 3p³', S: '1s² 2s² 2p⁶ 3s² 3p⁴', Cl: '1s² 2s² 2p⁶ 3s² 3p⁵', Ar: '1s² 2s² 2p⁶ 3s² 3p⁶',
    K: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s¹', Ca: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s²', Sc: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹', Ti: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d²', V: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d³', Cr: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s¹ 3d⁵', Mn: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d⁵', Fe: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d⁶',
    Co: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d⁷', Ni: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d⁸', Cu: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s¹ 3d¹⁰', Zn: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰', Ga: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p¹', Ge: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p²', As: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p³', Se: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁴', Br: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁵', Kr: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶',
    Rb: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s¹', Sr: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s²', Y: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4f¹', Zr: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹', Nb: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d²', Mo: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d³', Tc: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d⁴', Ru: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d⁵',
    Rh: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d⁶', Pd: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d⁷', Ag: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d⁸', Cd: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d⁹', In: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p¹', Sn: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p²', Sb: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p³', Te: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁴', I: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁵', Xe: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶',
    Cs: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s¹', Ba: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s²', La: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹', Ce: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f²', Pr: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f³', Nd: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f⁴', Pm: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f⁵', Sm: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f⁶', Eu: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f⁷', Gd: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f⁸',
    Tb: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f⁹', Dy: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁰', Ho: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹¹', Er: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹²', Tm: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹³', Yb: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴', Lu: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p¹', Hf: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p²', Ta: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p³', W: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁴',
    Re: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁵', Os: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶', Ir: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹', Pt: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d²', Au: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d³', Hg: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d⁴', Tl: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d⁵', Pb: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d⁶', Bi: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d⁷',
    Po: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d⁸', At: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d⁹', Rn: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰', Fr: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s¹', Ra: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s²', Ac: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f¹', Th: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f²', Pa: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f³', U: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f⁴', Np: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f⁵', Pu: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f⁶',
    Am: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f⁷', Cm: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f⁸', Bk: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f⁹', Cf: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f¹⁰', Es: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f¹¹', Fm: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f¹²', Md: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f¹³', No: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f¹⁴', Lr: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f¹⁴ 7p¹', Rf: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f¹⁴ 7p²', Db: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f¹⁴ 7p³',
    Sg: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f¹⁴ 7p⁴', Bh: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f¹⁴ 7p⁵', Hs: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f¹⁴ 7p⁶', Mt: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f¹⁴ 7p⁷', Ds: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f¹⁴ 7p⁸', Rg: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f¹⁴ 7p⁹', Cn: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f¹⁴ 7p¹⁰', Nh: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f¹⁴ 7p¹¹',
    Fl: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f¹⁴ 7p¹²', Mc: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f¹⁴ 7p¹³', Lv: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f¹⁴ 7p¹⁴', Ts: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f¹⁴ 7p¹⁵', Og: '1s² 2s² 2p⁶ 3s² 3p⁶ 4s² 3d¹⁰ 4p⁶ 5s² 4d¹⁰ 5p⁶ 6s² 5f¹⁴ 6p⁶ 6d¹⁰ 7s² 5f¹⁴ 7p¹⁶'
  },
  simplifiedElectronConfiguration: {
    Li: '[He] 2s¹', Be: '[He] 2s²', B: '[He] 2s² 2p¹', C: '[He] 2s² 2p²', N: '[He] 2s² 2p³', O: '[He] 2s² 2p⁴', F: '[He] 2s² 2p⁵', Ne: '[He] 2s² 2p⁶',
    Na: '[Ne] 3s¹', Mg: '[Ne] 3s²', Al: '[Ne] 3s² 3p¹', Si: '[Ne] 3s² 3p²', P: '[Ne] 3s² 3p³', S: '[Ne] 3s² 3p⁴', Cl: '[Ne] 3s² 3p⁵', Ar: '[Ne] 3s² 3p⁶',
    K: '[Ar] 4s¹', Ca: '[Ar] 4s²', Sc: '[Ar] 4s² 3d¹', Ti: '[Ar] 4s² 3d²', V: '[Ar] 4s² 3d³', Cr: '[Ar] 4s¹ 3d⁵', Mn: '[Ar] 4s² 3d⁵', Fe: '[Ar] 4s² 3d⁶', Co: '[Ar] 4s² 3d⁷', Ni: '[Ar] 4s² 3d⁸', Cu: '[Ar] 4s¹ 3d¹⁰', Zn: '[Ar] 4s² 3d¹⁰', Ga: '[Ar] 4s² 3d¹⁰ 4p¹', Ge: '[Ar] 4s² 3d¹⁰ 4p²', As: '[Ar] 4s² 3d¹⁰ 4p³', Se: '[Ar] 4s² 3d¹⁰ 4p⁴', Br: '[Ar] 4s² 3d¹⁰ 4p⁵', Kr: '[Ar] 4s² 3d¹⁰ 4p⁶',
    Rb: '[Kr] 5s¹', Sr: '[Kr] 5s²', Y: '[Kr] 5s² 4d¹', Zr: '[Kr] 5s² 4d²', Nb: '[Kr] 5s² 4d³', Mo: '[Kr] 5s² 4d⁴', Tc: '[Kr] 5s² 4d⁵', Ru: '[Kr] 5s² 4d⁶', Rh: '[Kr] 5s² 4d⁷', Pd: '[Kr] 5s² 4d⁸', Ag: '[Kr] 5s¹ 4d¹⁰', Cd: '[Kr] 5s² 4d¹⁰', In: '[Kr] 5s² 4d¹⁰ 5p¹', Sn: '[Kr] 5s² 4d¹⁰ 5p²', Sb: '[Kr] 5s² 4d¹⁰ 5p³', Te: '[Kr] 5s² 4d¹⁰ 5p⁴', I: '[Kr] 5s² 4d¹⁰ 5p⁵', Xe: '[Kr] 5s² 4d¹⁰ 5p⁶',
    Cs: '[Xe] 6s¹', Ba: '[Xe] 6s²', La: '[Xe] 6s² 5d¹', Ce: '[Xe] 6s² 4f¹', Pr: '[Xe] 6s² 4f²', Nd: '[Xe] 6s² 4f³', Pm: '[Xe] 6s² 4f⁴', Sm: '[Xe] 6s² 4f⁵', Eu: '[Xe] 6s² 4f⁶', Gd: '[Xe] 6s² 4f⁷', Tb: '[Xe] 6s² 4f⁸', Dy: '[Xe] 6s² 4f⁹', Ho: '[Xe] 6s² 4f¹⁰', Er: '[Xe] 6s² 4f¹¹', Tm: '[Xe] 6s² 4f¹²', Yb: '[Xe] 6s² 4f¹³', Lu: '[Xe] 6s² 4f¹⁴', Hf: '[Xe] 6s² 4f¹⁴ 5d¹', Ta: '[Xe] 6s² 4f¹⁴ 5d²', W: '[Xe] 6s² 4f¹⁴ 5d³', Re: '[Xe] 6s² 4f¹⁴ 5d⁴', Os: '[Xe] 6s² 4f¹⁴ 5d⁵', Ir: '[Xe] 6s² 4f¹⁴ 5d⁶', Pt: '[Xe] 6s² 4f¹⁴ 5d⁷', Au: '[Xe] 6s² 4f¹⁴ 5d�', Hg: '[Xe] 6s² 4f¹⁴ 5d⁹', Tl: '[Xe] 6s² 4f¹⁴ 5d¹⁰ 6p¹', Pb: '[Xe] 6s² 4f¹⁴ 5d¹⁰ 6p²', Bi: '[Xe] 6s² 4f¹⁴ 5d¹⁰ 6p³', Po: '[Xe] 6s² 4f¹⁴ 5d¹⁰ 6p⁴', At: '[Xe] 6s² 4f¹⁴ 5d¹⁰ 6p⁵', Rn: '[Xe] 6s² 4f¹⁴ 5d¹⁰ 6p⁶',
    Fr: '[Rn] 7s¹', Ra: '[Rn] 7s²', Ac: '[Rn] 7s² 5f¹', Th: '[Rn] 7s² 5f²', Pa: '[Rn] 7s² 5f³', U: '[Rn] 7s² 5f⁴', Np: '[Rn] 7s² 5f⁵', Pu: '[Rn] 7s² 5f⁶', Am: '[Rn] 7s² 5f⁷', Cm: '[Rn] 7s² 5f⁸', Bk: '[Rn] 7s² 5f⁹', Cf: '[Rn] 7s² 5f¹⁰', Es: '[Rn] 7s² 5f¹¹', Fm: '[Rn] 7s² 5f¹²', Md: '[Rn] 7s² 5f¹³', No: '[Rn] 7s² 5f¹⁴', Lr: '[Rn] 7s² 5f¹⁴ 6d¹', Rf: '[Rn] 7s² 5f¹⁴ 6d²', Db: '[Rn] 7s² 5f¹⁴ 6d³', Sg: '[Rn] 7s² 5f¹⁴ 6d⁴', Bh: '[Rn] 7s² 5f¹⁴ 6d⁵', Hs: '[Rn] 7s² 5f¹⁴ 6d⁶', Mt: '[Rn] 7s² 5f¹⁴ 6d⁷', Ds: '[Rn] 7s² 5f¹⁴ 6d⁸', Rg: '[Rn] 7s² 5f¹⁴ 6d⁹', Cn: '[Rn] 7s² 5f¹⁴ 6d¹⁰', Nh: '[Rn] 7s² 5f¹⁴ 6d¹¹', Fl: '[Rn] 7s² 5f¹⁴ 6d¹²', Mc: '[Rn] 7s² 5f¹⁴ 6d¹³', Lv: '[Rn] 7s² 5f¹⁴ 6d¹⁴', Ts: '[Rn] 7s² 5f¹⁴ 6d¹⁵', Og: '[Rn] 7s² 5f¹⁴ 6d¹⁶'
  },
  possibleIons: {
    H: ['H⁺', 'H²⁺'], He: [], Li: ['Li⁺'], Be: ['Be²⁺'], B: ['B³⁺'], C: [], N: [], O: [], F: [], Ne: [],
    Na: ['Na⁺'], Mg: ['Mg²⁺'], Al: ['Al³⁺'], Si: [], P: [], S: [], Cl: [], Ar: [],
    K: ['K⁺'], Ca: ['Ca²⁺'], Sc: ['Sc³⁺'], Ti: ['Ti⁴⁺'], V: ['V⁵⁺'], Cr: ['Cr⁶⁺'], Mn: ['Mn⁷⁺'], Fe: ['Fe²⁺', 'Fe³⁺'],
    Co: ['Co²⁺', 'Co³⁺'], Ni: ['Ni²⁺'], Cu: ['Cu¹⁺', 'Cu²⁺'], Zn: ['Zn²⁺'], Ga: ['Ga³⁺'], Ge: [], As: [], Se: [], Br: [], Kr: [],
    Rb: ['Rb⁺'], Sr: ['Sr²⁺'], Y: ['Y³⁺'], Zr: ['Zr⁴⁺'], Nb: ['Nb⁵⁺'], Mo: ['Mo⁶⁺'], Tc: ['Tc⁷⁺'], Ru: ['Ru⁴⁺'],
    Rh: ['Rh³⁺'], Pd: ['Pd²⁺'], Ag: ['Ag¹⁺', 'Ag²⁺'], Cd: ['Cd²⁺'], In: ['In³⁺'], Sn: ['Sn²⁺', 'Sn⁴⁺'], Sb: ['Sb³⁺', 'Sb⁵⁺'], Te: [], I: [], Xe: [],
    Cs: ['Cs⁺'], Ba: ['Ba²⁺'], La: ['La³⁺'], Ce: ['Ce³⁺', 'Ce⁴⁺'], Pr: ['Pr³⁺', 'Pr⁴⁺'], Nd: ['Nd³⁺', 'Nd⁴⁺'], Pm: ['Pm³⁺', 'Pm⁴⁺'], Sm: ['Sm³⁺', 'Sm⁴⁺'], Eu: ['Eu²⁺', 'Eu³⁺'], Gd: ['Gd³⁺'],
    Tb: ['Tb³⁺'], Dy: ['Dy³⁺'], Ho: ['Ho³⁺'], Er: ['Er³⁺'], Tm: ['Tm³⁺'], Yb: ['Yb²⁺', 'Yb³⁺'], Lu: ['Lu³⁺'], Hf: ['Hf⁴⁺'], Ta: ['Ta⁵⁺'], W: ['W⁶⁺'],
    Re: ['Re⁷⁺'], Os: ['Os⁴⁺', 'Os⁸⁺'], Ir: ['Ir³⁺', 'Ir⁴⁺'], Pt: ['Pt²⁺', 'Pt⁴⁺'], Au: ['Au¹⁺', 'Au³⁺'], Hg: ['Hg¹⁺', 'Hg²⁺'], Tl: ['Tl¹⁺', 'Tl³⁺'], Pb: ['Pb²⁺', 'Pb⁴⁺'], Bi: ['Bi³⁺', 'Bi⁵⁺'],
    Po: [], At: [], Rn: [], Fr: ['Fr¹⁺'], Ra: ['Ra²⁺'], Ac: ['Ac³⁺'], Th: ['Th⁴⁺'], Pa: ['Pa⁵⁺'], U: ['U⁶⁺'], Np: ['Np⁷⁺'], Pu: ['Pu⁴⁺', 'Pu⁶⁺', 'Pu⁸⁺'],
    Am: ['Am³⁺', 'Am⁴⁺', 'Am⁶⁺', 'Am⁷⁺', 'Am⁸⁺'], Cm: ['Cm³⁺', 'Cm⁴⁺', 'Cm⁶⁺', 'Cm⁷⁺', 'Cm⁸⁺'], Bk: ['Bk³⁺', 'Bk⁴⁺', 'Bk⁶⁺', 'Bk⁷⁺', 'Bk⁸⁺'], Cf: ['Cf³⁺', 'Cf⁴⁺', 'Cf⁶⁺', 'Cf⁷⁺', 'Cf⁸⁺'], Es: ['Es³⁺', 'Es⁴⁺', 'Es⁶⁺', 'Es⁷⁺', 'Es⁸⁺'], Fm: ['Fm³⁺', 'Fm⁴⁺', 'Fm⁶⁺', 'Fm⁷⁺', 'Fm⁸⁺'], Md: ['Md³⁺', 'Md⁴⁺', 'Md⁶⁺', 'Md⁷⁺', 'Md⁸⁺'], No: ['No³⁺', 'No⁴⁺', 'No⁶⁺', 'No⁷⁺', 'No⁸⁺'], Lr: ['Lr³⁺', 'Lr⁴⁺', 'Lr⁶⁺', 'Lr⁷⁺', 'Lr⁸⁺'], Rf: ['Rf⁴⁺', 'Rf⁶⁺', 'Rf⁸⁺'], Db: ['Db³⁺', 'Db⁴⁺', 'Db⁶⁺', 'Db⁷⁺', 'Db⁸⁺'],
    Sg: ['Sg³⁺', 'Sg⁴⁺', 'Sg⁶⁺', 'Sg⁷⁺', 'Sg⁸⁺'], Bh: ['Bh³⁺', 'Bh⁴⁺', 'Bh⁶⁺', 'Bh⁷⁺', 'Bh⁸⁺'], Hs: ['Hs⁴⁺', 'Hs⁶⁺', 'Hs⁸⁺'], Mt: ['Mt³⁺', 'Mt⁴⁺', 'Mt⁶⁺', 'Mt⁷⁺', 'Mt⁸⁺'], Ds: ['Ds³⁺', 'Ds⁴⁺', 'Ds⁶⁺', 'Ds⁷⁺', 'Ds⁸⁺'], Rg: ['Rg³⁺', 'Rg⁴⁺', 'Rg⁶⁺', 'Rg⁷⁺', 'Rg⁸⁺'], Cn: ['Cn³⁺', 'Cn⁴⁺', 'Cn⁶⁺', 'Cn⁷⁺', 'Cn⁸⁺'], Nh: ['Nh³⁺', 'Nh⁴⁺', 'Nh⁶⁺', 'Nh⁷⁺', 'Nh⁸⁺'],
    Fl: ['Fl³⁺', 'Fl⁴⁺', 'Fl⁶⁺', 'Fl⁷⁺', 'Fl⁸⁺'], Mc: ['Mc³⁺', 'Mc⁴⁺', 'Mc⁶⁺', 'Mc⁷⁺', 'Mc⁸⁺'], Lv: ['Lv³⁺', 'Lv⁴⁺', 'Lv⁶⁺', 'Lv⁷⁺', 'Lv⁸⁺'], Ts: ['Ts³⁺', 'Ts⁴⁺', 'Ts⁶⁺', 'Ts⁷⁺', 'Ts⁸⁺'], Og: ['Og⁶⁺', 'Og⁸⁺']
  },

  // Dados de isótopos e abundância natural
  isotopes: {
    H: [
      { mass: 1.008, abundance: 99.985, name: 'Protium' },
      { mass: 2.014, abundance: 0.015, name: 'Deuterium' },
      { mass: 3.016, abundance: 0, name: 'Tritium (radioactive)' }
    ],
    He: [
      { mass: 3.016, abundance: 0.000137, name: 'Helium-3' },
      { mass: 4.003, abundance: 99.999863, name: 'Helium-4' }
    ],
    Li: [
      { mass: 6.015, abundance: 7.59, name: 'Lithium-6' },
      { mass: 7.016, abundance: 92.41, name: 'Lithium-7' }
    ],
    Be: [
      { mass: 9.012, abundance: 100, name: 'Beryllium-9' }
    ],
    B: [
      { mass: 10.013, abundance: 19.9, name: 'Boron-10' },
      { mass: 11.009, abundance: 80.1, name: 'Boron-11' }
    ],
    C: [
      { mass: 12.000, abundance: 98.93, name: 'Carbon-12' },
      { mass: 13.003, abundance: 1.07, name: 'Carbon-13' },
      { mass: 14.003, abundance: 0, name: 'Carbon-14 (radioactive)' }
    ],
    N: [
      { mass: 14.003, abundance: 99.636, name: 'Nitrogen-14' },
      { mass: 15.000, abundance: 0.364, name: 'Nitrogen-15' }
    ],
    O: [
      { mass: 15.995, abundance: 99.757, name: 'Oxygen-16' },
      { mass: 16.999, abundance: 0.038, name: 'Oxygen-17' },
      { mass: 17.999, abundance: 0.205, name: 'Oxygen-18' }
    ],
    F: [
      { mass: 18.998, abundance: 100, name: 'Fluorine-19' }
    ],
    Ne: [
      { mass: 19.992, abundance: 90.48, name: 'Neon-20' },
      { mass: 20.994, abundance: 0.27, name: 'Neon-21' },
      { mass: 21.991, abundance: 9.25, name: 'Neon-22' }
    ],
    Na: [
      { mass: 22.990, abundance: 100, name: 'Sodium-23' }
    ],
    Mg: [
      { mass: 23.985, abundance: 78.99, name: 'Magnesium-24' },
      { mass: 24.986, abundance: 10.00, name: 'Magnesium-25' },
      { mass: 25.983, abundance: 11.01, name: 'Magnesium-26' }
    ],
    Al: [
      { mass: 26.982, abundance: 100, name: 'Aluminum-27' }
    ],
    Si: [
      { mass: 27.977, abundance: 92.223, name: 'Silicon-28' },
      { mass: 28.976, abundance: 4.685, name: 'Silicon-29' },
      { mass: 29.974, abundance: 3.092, name: 'Silicon-30' }
    ],
    P: [
      { mass: 30.974, abundance: 100, name: 'Phosphorus-31' }
    ],
    S: [
      { mass: 31.972, abundance: 94.99, name: 'Sulfur-32' },
      { mass: 32.971, abundance: 0.75, name: 'Sulfur-33' },
      { mass: 33.968, abundance: 4.25, name: 'Sulfur-34' },
      { mass: 35.967, abundance: 0.01, name: 'Sulfur-36' }
    ],
    Cl: [
      { mass: 34.969, abundance: 75.76, name: 'Chlorine-35' },
      { mass: 36.966, abundance: 24.24, name: 'Chlorine-37' }
    ],
    Ar: [
      { mass: 35.968, abundance: 0.3365, name: 'Argon-36' },
      { mass: 37.963, abundance: 0.0632, name: 'Argon-38' },
      { mass: 39.962, abundance: 99.6003, name: 'Argon-40' }
    ],
    K: [
      { mass: 38.964, abundance: 93.2581, name: 'Potassium-39' },
      { mass: 39.964, abundance: 0.0117, name: 'Potassium-40 (radioactive)' },
      { mass: 40.962, abundance: 6.7302, name: 'Potassium-41' }
    ],
    Ca: [
      { mass: 39.963, abundance: 96.941, name: 'Calcium-40' },
      { mass: 41.959, abundance: 0.647, name: 'Calcium-42' },
      { mass: 42.959, abundance: 0.135, name: 'Calcium-43' },
      { mass: 43.955, abundance: 2.086, name: 'Calcium-44' },
      { mass: 45.954, abundance: 0.004, name: 'Calcium-46' },
      { mass: 47.953, abundance: 0.187, name: 'Calcium-48' }
    ],
    Fe: [
      { mass: 53.940, abundance: 5.845, name: 'Iron-54' },
      { mass: 55.935, abundance: 91.754, name: 'Iron-56' },
      { mass: 56.935, abundance: 2.119, name: 'Iron-57' },
      { mass: 57.933, abundance: 0.282, name: 'Iron-58' }
    ],
    Cu: [
      { mass: 62.930, abundance: 69.15, name: 'Copper-63' },
      { mass: 64.928, abundance: 30.85, name: 'Copper-65' }
    ],
    Zn: [
      { mass: 63.929, abundance: 49.17, name: 'Zinc-64' },
      { mass: 65.926, abundance: 27.73, name: 'Zinc-66' },
      { mass: 66.927, abundance: 4.04, name: 'Zinc-67' },
      { mass: 67.925, abundance: 18.45, name: 'Zinc-68' },
      { mass: 69.925, abundance: 0.61, name: 'Zinc-70' }
    ],
    Br: [
      { mass: 78.918, abundance: 50.69, name: 'Bromine-79' },
      { mass: 80.916, abundance: 49.31, name: 'Bromine-81' }
    ],
    Ag: [
      { mass: 106.905, abundance: 51.839, name: 'Silver-107' },
      { mass: 108.905, abundance: 48.161, name: 'Silver-109' }
    ],
    I: [
      { mass: 126.904, abundance: 100, name: 'Iodine-127' }
    ],
    Au: [
      { mass: 196.967, abundance: 100, name: 'Gold-197' }
    ],
    Hg: [
      { mass: 195.966, abundance: 0.15, name: 'Mercury-196' },
      { mass: 197.967, abundance: 9.97, name: 'Mercury-198' },
      { mass: 198.968, abundance: 16.87, name: 'Mercury-199' },
      { mass: 199.968, abundance: 23.10, name: 'Mercury-200' },
      { mass: 200.970, abundance: 13.18, name: 'Mercury-201' },
      { mass: 201.971, abundance: 29.86, name: 'Mercury-202' },
      { mass: 203.973, abundance: 6.87, name: 'Mercury-204' }
    ],
    Pb: [
      { mass: 203.973, abundance: 1.4, name: 'Lead-204' },
      { mass: 205.974, abundance: 24.1, name: 'Lead-206' },
      { mass: 206.976, abundance: 22.1, name: 'Lead-207' },
      { mass: 207.977, abundance: 52.4, name: 'Lead-208' }
    ],
    U: [
      { mass: 234.041, abundance: 0.0054, name: 'Uranium-234 (radioactive)' },
      { mass: 235.044, abundance: 0.7204, name: 'Uranium-235 (radioactive)' },
      { mass: 238.051, abundance: 99.2742, name: 'Uranium-238 (radioactive)' }
    ]
  },

};
